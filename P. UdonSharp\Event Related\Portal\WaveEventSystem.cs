﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class WaveEventSystem : UdonSharpBehaviour
{
    public bool IsFinished;
    public int EnderType; //1 is Enemies, 2 is Portals
    public GameObject FinishedObject;

    public GameObject[] RemainingEnemies1, RemainingEnemies2, RemainingEnemies3;
    public GameObject[] Waves;
    public int EnemiesRemain;
    public int CurrentWave;

    public GameObject[] MainEventObject;
    public float DisableDelay = 20;

    void Start(){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}

    void OnEnable(){
        IsFinished = false;
        FinishedObject.SetActive(false);
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);
    }

    public void UpdateCustom(){
        if(!IsFinished){
            if(EnderType == 0){
                if(CurrentWave == 0){
                    EnemiesRemain = 0;
                    Waves[0].SetActive(true);
                    Waves[1].SetActive(false);
                    Waves[2].SetActive(false);
                    for (int i = 0; i < RemainingEnemies1.Length; i++){if(RemainingEnemies1[i] != null){EnemiesRemain++;}}

                    if(EnemiesRemain > 0){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}
                    else{
                        CurrentWave += 1;
                        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 15f);
                    }
                }
                else if(CurrentWave == 1){
                    EnemiesRemain = 0;
                    Waves[0].SetActive(false);
                    Waves[1].SetActive(true);
                    Waves[2].SetActive(false);
                    for (int i = 0; i < RemainingEnemies2.Length; i++){if(RemainingEnemies2[i] != null){EnemiesRemain++;}}

                    if(EnemiesRemain > 0){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}
                    else{
                        CurrentWave += 1;
                        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 15f);
                    }
                }
                else if(CurrentWave == 2){
                    EnemiesRemain = 0;
                    Waves[0].SetActive(false);
                    Waves[1].SetActive(false);
                    Waves[2].SetActive(true);
                    for (int i = 0; i < RemainingEnemies3.Length; i++){if(RemainingEnemies3[i] != null){EnemiesRemain++;}}

                    if(EnemiesRemain > 0){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}
                    else{
                        CurrentWave += 1;
                        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 15f);
                    }
                }
                else if(CurrentWave == 3){
                    IsFinished = true; 
                    Waves[0].SetActive(false);
                    Waves[1].SetActive(false);
                    Waves[2].SetActive(false);
                    FinishedObject.SetActive(true);
                    SendCustomEventDelayedSeconds(nameof(DeactivateMainEventObjects), DisableDelay);
                }
            }
        }
    }
    public void DeactivateMainEventObjects(){
        if(MainEventObject == null) return;
        for (int i = 0; i < MainEventObject.Length; i++){MainEventObject[i].SetActive(false);}
    }
}
