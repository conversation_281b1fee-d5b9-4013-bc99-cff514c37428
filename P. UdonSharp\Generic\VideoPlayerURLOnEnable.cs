﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Video.Components.Base;
using VRC.SDK3.Video.Components;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class VideoPlayerURLOnEnable : UdonSharpBehaviour
{
    public VRCUnityVideoPlayer videoPlayer;
    public VRCUrl URL;
    public bool CanBeLooped;

    public void OnEnable()
    {   
        if (videoPlayer != null && URL != null)
        {
            videoPlayer.PlayURL(URL);
            videoPlayer.Loop = CanBeLooped;
        }
    }
    public void OnDisable()
    {
        if (videoPlayer != null)
        {
            videoPlayer.Loop = false;
        }
    }
}