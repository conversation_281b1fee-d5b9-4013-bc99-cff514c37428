; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 21
		Hour: 0
		Minute: 35
		Second: 46
		Millisecond: 964
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\crcbdgdd_Armor Plating Hat Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\crcbdgdd_Armor Plating Hat Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2410575215776, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2405582868496, "Geometry::Scene", "Mesh" {
		Vertices: *57 {
			a: -29.9999982118607,2.06169188022614,51.9615292549133,-60.0000023841858,2.06169188022614,0,-29.9999982118607,22.0616936683655,51.9615292549133,-60.0000023841858,22.0616936683655,0,-0,-22.0617294311523,0,-50,22.0616936683655,0,-24.9999985098839,22.0616936683655,43.3012723922729,30.0000071525574,2.06169188022614,51.9615232944489,30.0000071525574,22.0616936683655,51.9615232944489,25,22.0616936683655,43.3012723922729,60.0000023841858,2.06169188022614,-5.96046447753906e-06,60.0000023841858,22.0616936683655,-5.96046447753906e-06,50,22.0616936683655,-2.98023223876953e-06,29.9999952316284,2.06169188022614,-51.9615292549133,29.9999952316284,22.0616936683655,-51.9615292549133,25,22.0616936683655,-43.3012753725052,-29.9999952316284,2.06169188022614,-51.9615292549133,-29.9999952316284,22.0616936683655,-51.9615292549133,-24.9999970197678,22.0616936683655,-43.3012753725052
		} 
		PolygonVertexIndex: *108 {
			a: 4,5,-7,4,6,-10,4,9,-13,4,12,-16,4,15,-19,4,18,-6,1,4,-1,0,4,-8,7,4,-11,10,4,-14,13,4,-17,16,4,-2,0,3,-2,3,0,-3,7,2,-1,2,7,-9,10,8,-8,8,10,-12,13,11,-11,11,13,-15,16,14,-14,14,16,-18,1,17,-17,17,1,-4,2,5,-4,5,2,-7,8,6,-3,6,8,-10,11,9,-9,9,11,-13,14,12,-12,12,14,-16,17,15,-15,15,17,-19,3,18,-18,18,3,-6
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *324 {
				a: -1.41829490374334e-08,1,-7.09147451871672e-09,0.661670327186584,0.749794900417328,-1.59514534914251e-08,0.330835163593292,0.749794900417328,-0.573023319244385,-1.41829490374334e-08,1,-7.09147451871672e-09,0.330835163593292,0.749794900417328,-0.573023319244385,-0.330835193395615,0.749794900417328,-0.573023319244385,-1.41829490374334e-08,1,-7.09147451871672e-09,-0.330835193395615,0.749794900417328,-0.573023319244385,-0.661670386791229,0.749794900417328,0,-1.41829490374334e-08,1,-7.09147451871672e-09,-0.661670386791229,0.749794900417328,0,-0.330835163593292,0.749794900417328,0.573023319244385,-1.41829490374334e-08,1,-7.09147451871672e-09,-0.330835163593292,0.749794900417328,0.573023319244385,0.330835163593292,0.749794900417328,0.573023319244385,-1.41829490374334e-08,1,-7.09147451871672e-09,0.330835163593292,0.749794900417328,0.573023319244385,0.661670327186584,0.749794900417328,-1.59514534914251e-08,-0.364673525094986,-0.90701949596405,0.210544377565384,-0.364673525094986,-0.90701949596405,0.210544377565384,-0.364673525094986,-0.90701949596405,0.210544377565384,4.18314058947544e-08,-0.90701949596405,0.421088755130768,4.18314058947544e-08,-0.90701949596405,0.421088755130768,4.18314058947544e-08,-0.90701949596405,0.421088755130768,0.364673554897308,-0.90701949596405,0.2105443328619,0.364673554897308,-0.90701949596405,0.2105443328619,0.364673554897308,-0.90701949596405,0.2105443328619,0.364673525094986,-0.90701949596405,-0.210544422268867,0.364673525094986,-0.90701949596405,-0.210544422268867,0.364673525094986,-0.90701949596405,-0.210544422268867,-0,-0.90701949596405,-0.421088695526123,-0,-0.90701949596405,-0.421088695526123,-0,-0.90701949596405,-0.421088695526123,-0.364673525094986,-0.90701949596405,-0.210544407367706,-0.364673525094986,-0.90701949596405,-0.210544407367706,-0.364673525094986,-0.90701949596405,-0.210544407367706,-0.499999940395355,0,0.866025507450104,-1,0,-3.44127606410893e-08,-1,0,-3.44127606410893e-08,-1,0,-3.44127606410893e-08,-0.499999940395355,0,0.866025507450104,-0.499999940395355,0,0.866025507450104,
0.50000011920929,0,0.866025328636169,-0.499999940395355,0,0.866025507450104,-0.499999940395355,0,0.866025507450104,-0.499999940395355,0,0.866025507450104,0.50000011920929,0,0.866025328636169,0.50000011920929,0,0.866025328636169,1,0,-1.20444653362028e-07,0.50000011920929,0,0.866025328636169,0.50000011920929,0,0.866025328636169,0.50000011920929,0,0.866025328636169,1,0,-1.20444653362028e-07,1,0,-1.20444653362028e-07,0.499999940395355,0,-0.866025447845459,1,0,-1.20444653362028e-07,1,0,-1.20444653362028e-07,1,0,-1.20444653362028e-07,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,-0.5,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-1,0,-3.44127606410893e-08,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-1,0,-3.44127606410893e-08,-1,0,-3.44127606410893e-08,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0
			} 
			NormalsW: *108 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *324 {
				a: 1.32180151268813e-08,7.09147451871672e-09,1,1.4467469888757e-08,8.5073379452183e-09,1,0.391385316848755,0.443511873483658,0.806296944618225,5.64183458652752e-25,7.09147451871672e-09,1,-5.35846123028705e-09,0.607216536998749,0.794536411762238,-6.45076081440266e-09,0.607216596603394,0.794536411762238,-2.32134862443445e-08,7.09147407462751e-09,1,-0.391385287046432,0.443511873483658,0.80629700422287,-9.53224343902548e-09,-8.41190495037836e-09,1.00000011920929,2.55065959464673e-08,7.09147496280593e-09,1,1.78578165588306e-08,1.57589603588804e-08,1,0.39138525724411,-0.443511873483658,0.806296944618225,-5.64183458652752e-25,-7.09147451871672e-09,-1,5.35846123028705e-09,0.607216536998749,-0.794536411762238,-5.35846123028705e-09,0.607216536998749,-0.794536411762238,-1.46220324737101e-08,7.09147451871672e-09,1,-0.391385316848755,-0.443511873483658,0.806296944618225,-1.18399179349638e-09,2.23192593296062e-08,1,0.0785405039787292,0.195346683263779,0.977584362030029,0.0785405039787292,0.195346683263779,0.977584362030029,0.0785405039787292,0.195346683263779,0.977584362030029,-9.23678911135539e-09,0.421088755130768,0.90701949596405,-9.23678911135539e-09,0.421088755130768,0.90701949596405,-9.23678911135539e-09,0.421088755130768,0.90701949596405,-0.0785404890775681,0.195346638560295,0.977584362030029,-0.0785404890775681,0.195346638560295,0.977584362030029,-0.0785404890775681,0.195346638560295,0.977584362030029,0.0785405114293098,-0.195346727967262,0.977584362030029,0.0785405114293098,-0.195346727967262,0.977584362030029,0.0785405114293098,-0.195346727967262,0.977584362030029,0,-0.421088695526123,0.90701949596405,0,-0.421088695526123,0.90701949596405,0,-0.421088695526123,0.90701949596405,-0.0785405188798904,-0.195346713066101,0.977584362030029,-0.0785405188798904,-0.195346713066101,0.977584362030029,-0.0785405188798904,-0.195346713066101,0.977584362030029,0,1.00000011920929,-0,0,1,-0,0,1,-0,0,1,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,-0,1,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,
-0,1,-0,-0,1,-0,0,1,0,-0,1,-0,-0,1,-0,-0,1,-0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1.00000011920929,-0,0,1,0,0,1,0,0,1,0,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1
			} 
			BinormalsW: *108 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *324 {
				a: -1,0,1.32180151268813e-08,-0.749794960021973,0.661670327186584,5.21858245505769e-09,-0.858699977397919,0.491024285554886,0.146729379892349,-1,-1.41829490374334e-08,1.00578022263401e-16,-0.943688571453094,0.26286056637764,-0.200888589024544,-0.943688571453094,-0.262860625982285,0.200888618826866,-1,0,-2.32134862443445e-08,-0.858699977397919,-0.491024315357208,-0.146729350090027,-0.749794900417328,-0.661670386791229,-1.27131345450948e-08,-1,0,2.55065959464673e-08,-0.749794840812683,-0.661670386791229,2.3816934202614e-08,-0.858699977397919,-0.491024255752563,0.146729350090027,1,1.41829490374334e-08,-1.00578022263401e-16,0.943688571453094,0.26286056637764,0.200888589024544,0.943688571453094,-0.26286056637764,-0.200888589024544,-1,0,-1.46220324737101e-08,-0.858699977397919,0.491024285554886,-0.146729379892349,-0.749794960021973,0.661670327186584,-1.56557415920133e-08,0.927817165851593,-0.373035371303558,-8.92832474619354e-10,0.927817165851593,-0.373035371303558,-8.92832474619354e-10,0.927817165851593,-0.373035371303558,-8.92832474619354e-10,1,4.18314023420407e-08,-9.23678467046329e-09,1,4.18314023420407e-08,-9.23678467046329e-09,1,4.18314023420407e-08,-9.23678467046329e-09,0.927817106246948,0.373035371303558,4.30023394670798e-09,0.927817106246948,0.373035371303558,4.30023394670798e-09,0.927817106246948,0.373035371303558,4.30023394670798e-09,0.927817165851593,0.373035371303558,3.40740258231165e-09,0.927817165851593,0.373035371303558,3.40740258231165e-09,0.927817165851593,0.373035371303558,3.40740258231165e-09,1,0,0,1,0,0,1,0,0,0.927817165851593,-0.373035371303558,6.27471230529864e-09,0.927817165851593,-0.373035371303558,6.27471230529864e-09,0.927817165851593,-0.373035371303558,6.27471230529864e-09,0.866025507450104,0,0.499999910593033,-0,0,1,-0,0,1,-0,0,1,0.866025507450104,0,0.499999910593033,0.866025507450104,0,0.499999910593033,0.866025328636169,0,-0.500000178813934,0.866025447845459,0,0.499999970197678,0.866025447845459,0,0.499999970197678,0.866025447845459,0,0.499999970197678,0.866025328636169,0,-0.500000178813934,
0.866025328636169,0,-0.500000178813934,-1.37651028353503e-07,0,-1,0.866025328636169,0,-0.50000011920929,0.866025328636169,0,-0.50000011920929,0.866025328636169,0,-0.50000011920929,-1.37651028353503e-07,0,-1,-1.37651028353503e-07,0,-1,-0.866025447845459,0,-0.499999970197678,-1.37651028353503e-07,0,-1,-1.37651028353503e-07,0,-1,-1.37651028353503e-07,0,-1,-0.866025447845459,0,-0.499999970197678,-0.866025447845459,0,-0.499999970197678,-0.866025447845459,0,0.500000059604645,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,0.500000059604645,-0.866025447845459,0,0.500000059604645,-6.88255141767513e-08,0,1,-0.866025447845459,0,0.499999970197678,-0.866025447845459,0,0.499999970197678,-0.866025447845459,0,0.499999970197678,-6.88255141767513e-08,0,1,-6.88255141767513e-08,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *108 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *168 {
				a: 0.300000011920929,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.300000011920929,0.220616936683655,-0.300000041723251,0.220616936683655,-0.145975917577744,-0.0590313747525215,0.520873129367828,-0.0590313784778118,0.333424389362335,0.404501795768738,0.300000011920929,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.300000011920929,0.220616936683655,-0.300000041723251,0.220616936683655,0,-0.157460078597069,0.249999985098839,0.460753262042999,-0.25,0.460753262042999,0.300000011920929,0.0206169188022614,-0.300000011920929,0.0206169188022614,0.300000011920929,0.220616936683655,-0.300000011920929,0.220616936683655,0.145975932478905,-0.0590313673019409,-0.333424419164658,0.404501795768738,-0.520873129367828,-0.0590314082801342,0.299999982118607,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.299999982118607,0.220616936683655,-0.300000041723251,0.220616936683655,0.145975917577744,0.0590313673019409,-0.520873129367828,0.0590313524007797,-0.333424419164658,-0.40450182557106,0.299999952316284,0.0206169188022614,-0.299999952316284,0.0206169188022614,0.299999952316284,0.220616936683655,-0.299999952316284,0.220616936683655,0,-0.157460063695908,0.25,0.460753262042999,-0.249999970197678,0.460753262042999,0.300000041723251,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.300000041723251,0.220616936683655,-0.300000041723251,0.220616936683655,-0.145975917577744,0.0590313673019409,0.333424389362335,-0.40450182557106,0.520873129367828,0.0590313784778118,-0.564381122589111,-0.0430968515574932,-0.28603595495224,0.488433063030243,0.0822980478405952,-0.0430968552827835,0.299999982118607,0.519615292549133,0.600000023841858,0,0.249999985098839,0.433012723922729,0.5,0,-0.299999982118607,0.479982733726501,0.300000071525574,0.479982674121857,-1.01747890113302e-08,-0.0928994566202164,-0.300000071525574,0.519615232944489,0.299999982118607,0.519615292549133,-0.25,0.433012723922729,0.249999985098839,0.433012723922729,0.286036044359207,0.488433003425598,0.564381122589111,-0.0430968999862671,
-0.0822980552911758,-0.0430968441069126,-0.600000023841858,-5.96046447753906e-08,-0.300000071525574,0.519615232944489,-0.5,-2.98023223876953e-08,-0.25,0.433012723922729,0.564381122589111,0.0430968031287193,0.286035925149918,-0.488433063030243,-0.0822980478405952,0.0430968664586544,-0.299999952316284,-0.519615292549133,-0.600000023841858,-5.96046447753906e-08,-0.25,-0.433012753725052,-0.5,-2.98023223876953e-08,0.299999952316284,-0.479982763528824,-0.299999952316284,-0.479982763528824,0,0.0928994491696358,0.299999952316284,-0.519615292549133,-0.299999952316284,-0.519615292549133,0.249999970197678,-0.433012753725052,-0.25,-0.433012753725052,-0.286035925149918,-0.488433063030243,-0.564381122589111,0.0430968552827835,0.0822980478405952,0.0430968627333641,0.600000023841858,0,0.299999952316284,-0.519615292549133,0.5,0,0.249999970197678,-0.433012753725052
			} 
			UVIndex: *108 {
				a: 4,5,6,11,12,13,18,19,20,25,26,27,32,33,34,39,40,41,42,44,43,49,51,50,56,58,57,63,65,64,70,72,71,77,79,78,0,3,1,3,0,2,7,10,8,10,7,9,14,17,15,17,14,16,21,24,22,24,21,23,28,31,29,31,28,30,35,38,36,38,35,37,45,48,46,48,45,47,52,55,53,55,52,54,59,62,60,62,59,61,66,69,67,69,66,68,73,76,74,76,73,75,80,83,81,83,80,82
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2405848749328, "Model::Armor_Plating_Hat_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",5.94582414627075,-180,-180
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.600000023841858,0.600000083446503,0.600000083446503
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2405957748592, "Material::Interstellar_Armor_Texture_2", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2407160992080, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
	}
	Texture: 2407160994480, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Armor_Plating_Hat_Model, Model::RootNode
	C: "OO",2405848749328,0
	
	;Material::Interstellar_Armor_Texture_2, Model::Armor_Plating_Hat_Model
	C: "OO",2405957748592,2405848749328
	
	;Geometry::Scene, Model::Armor_Plating_Hat_Model
	C: "OO",2405582868496,2405848749328
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture_2
	C: "OO",2407160994480,2405957748592
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture_2
	C: "OP",2407160994480,2405957748592, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2407160992080,2407160994480
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
