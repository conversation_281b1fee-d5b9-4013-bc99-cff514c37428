﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class EdibleCubixLocal : UdonSharpBehaviour
{

    public VRC_Pickup pickup;
    private VRCPlayerApi localPlayer;  
    public Rigidbody rb;
    public int CubixType; //0 is blue, 1 is Green, 2 is Red

    //Effects
    public ParticleSystem Particle;
    public GameObject Body;
    public AudioSource EatingSound;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        rb = GetComponent<Rigidbody>();
        pickup = GetComponent<VRC_Pickup>();
    }

    public override void OnDrop()
    {
        if(CubixType == 0){
            float SetWalkSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;     // Steps of 0.5
            float SetRunSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetStrafeSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetJumpImpulseValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetGravityStrengthValue = Mathf.Round(Random.Range(0.1f, 2f) * 10f) / 10f;  // Steps of 0.1

            localPlayer.SetWalkSpeed(SetWalkSpeedValue);
            localPlayer.SetRunSpeed(SetRunSpeedValue);
            localPlayer.SetStrafeSpeed(SetStrafeSpeedValue);
            localPlayer.SetJumpImpulse(SetJumpImpulseValue);
            localPlayer.SetGravityStrength(SetGravityStrengthValue);

            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){
                int RandomHealth = Random.Range(1, playerSystem.MAXHEALTH+1);
                playerSystem.HEALTH = RandomHealth;
            }
        }
        if(CubixType == 1){
            localPlayer.SetWalkSpeed(4f);
            localPlayer.SetRunSpeed(7f);
            localPlayer.SetStrafeSpeed(8f);
            localPlayer.SetJumpImpulse(5f);
            localPlayer.SetGravityStrength(1f);

            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){playerSystem.HEALTH = playerSystem.MAXHEALTH;}
        }
        if(CubixType == 2){
            float SetWalkSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;     // Steps of 0.5
            float SetRunSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
            float SetStrafeSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
            float SetJumpImpulseValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
            float SetGravityStrengthValue = Mathf.Round(Random.Range(0.1f, 1f) * 10f) / 10f;  // Steps of 0.1

            localPlayer.SetWalkSpeed(SetWalkSpeedValue);
            localPlayer.SetRunSpeed(SetRunSpeedValue);
            localPlayer.SetStrafeSpeed(SetStrafeSpeedValue);
            localPlayer.SetJumpImpulse(SetJumpImpulseValue);

            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){
                int RandomHealth = Random.Range(1, playerSystem.MAXHEALTH/6);
                playerSystem.HEALTH = RandomHealth;
            }
        }
        if(CubixType == 3){
            localPlayer.SetWalkSpeed(Mathf.Round((localPlayer.GetWalkSpeed()/1.1f)  * 4f) / 4f);
            localPlayer.SetRunSpeed(Mathf.Round((localPlayer.GetRunSpeed()/1.1f)  * 4f) / 4f);
            localPlayer.SetStrafeSpeed(Mathf.Round((localPlayer.GetStrafeSpeed()/1.1f)  * 4f) / 4f);
            localPlayer.SetJumpImpulse(Mathf.Round((localPlayer.GetJumpImpulse()/1.1f)  * 4f) / 4f);
            localPlayer.SetGravityStrength(Mathf.Round((localPlayer.GetGravityStrength()/1.1f)  * 10f) / 10f);

            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){playerSystem.ARMOR = playerSystem.MAXARMOR;}
        }
        if(CubixType == 4){
            localPlayer.SetWalkSpeed(3f);
            localPlayer.SetRunSpeed(5f);
            localPlayer.SetStrafeSpeed(5f);
            localPlayer.SetJumpImpulse(3f);
            localPlayer.SetGravityStrength(1.1f);

            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){playerSystem.ProtectionBoostedFunction();}
        }
        if(CubixType == 5){
            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
            if(playerSystem){playerSystem.FireBoostedFunction();}
        }

        pickup.pickupable = false;
        Particle.Play();
        Body.SetActive(false);
        EatingSound.Play();
        SendCustomEventDelayedSeconds(nameof(Delete), 1f);
    }
    public void Delete(){Destroy(gameObject);}
}