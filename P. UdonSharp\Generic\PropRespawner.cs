using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class PropRespawner : UdonSharpBehaviour
{
    public GameObject[] Props;
    private Vector3[] initialPositions;
    private Quaternion[] initialRotations;

    public bool Cooldown = false;

    public void Start()
    {
        int count = Props.Length;
        initialPositions = new Vector3[count];
        initialRotations = new Quaternion[count];

        for (int i = 0; i < count; i++)
        {
            if(Props[i] != null){
                initialPositions[i] = Props[i].transform.position;
                initialRotations[i] = Props[i].transform.rotation;
            }
        }
    }

    public void CustomUpdate(){Cooldown = false;}

    public void Starter()
    {
        if(Cooldown == false){
            Cooldown = true;
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(PropRespawnerFunction));
            SendCustomEventDelayedSeconds(nameof(CustomUpdate), 60f);
        }
    }

    public void PropRespawnerFunction()
    {
        for (int i = 0; i < Props.Length; i++)
        {
            if(Props[i] != null){
                Props[i].transform.parent = null;
                Props[i].transform.position = initialPositions[i];
                Props[i].transform.rotation = initialRotations[i];
            }
        }
    }
}
