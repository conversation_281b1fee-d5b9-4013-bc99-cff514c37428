; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 19
		Hour: 1
		Minute: 13
		Second: 59
		Millisecond: 247
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\og6l3mub_Crack.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\og6l3mub_Crack.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2419977511792, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2423290377696, "Geometry::Scene", "Mesh" {
		Vertices: *108 {
			a: -13.435959815979,-73.986029624939,-22.491455078125,-18.4359550476074,-73.986029624939,-22.491455078125,3.97725328803062,-23.3720779418945,-22.4914163351059,-1.02274641394615,-23.3720779418945,-22.4914163351059,-18.4359550476074,-73.986029624939,-27.4913787841797,-1.02274641394615,-23.3720779418945,-27.4914175271988,-13.435959815979,-73.986029624939,-27.4913787841797,3.97725328803062,-23.3720779418945,-27.4914175271988,3.43862771987915,177.89249420166,27.491569519043,-1.56136751174927,177.89249420166,27.491569519043,3.43862771987915,177.89249420166,22.4916458129883,-1.56136751174927,177.89249420166,22.4916458129883,-15.728485584259,-177.892506122589,-7.92198181152344,-20.7284808158875,-177.892506122589,-7.92198181152344,-15.728485584259,-177.892506122589,-2.92205810546875,-20.7284808158875,-177.892506122589,-2.92205810546875,-13.2437825202942,-1.62180662155151,-13.8660430908203,-18.2437777519226,-1.62180662155151,-13.8660430908203,-13.2437825202942,-1.62180662155151,-18.865966796875,-18.2437777519226,-1.62180662155151,-18.865966796875,13.6715650558472,57.6583504676819,2.26383209228516,8.67156982421875,57.6583504676819,2.26383209228516,13.6715650558472,57.6583504676819,-2.73609161376953,8.67156982421875,57.6583504676819,-2.73609161376953,-13.3084774017334,93.6294674873352,15.6936645507812,-18.3084726333618,93.6294674873352,15.6936645507812,-13.3084774017334,93.6294674873352,10.6937408447266,-18.3084726333618,93.6294674873352,10.6937408447266,14.9854063987732,128.132402896881,19.2996978759766,9.98541116714478,128.132402896881,19.2996978759766,14.9854063987732,128.132402896881,14.2997741699219,9.98541116714478,128.132402896881,14.2997741699219,20.7284927368164,-131.790208816528,-16.8952941894531,15.728497505188,-131.790208816528,-16.8952941894531,20.7284927368164,-131.790208816528,-11.8953704833984,15.728497505188,-131.790208816528,-11.8953704833984
		} 
		PolygonVertexIndex: *136 {
			a: 0,2,3,-2,1,3,5,-5,4,5,7,-7,6,7,2,-1,8,10,11,-10,12,14,15,-14,2,16,17,-4,7,18,16,-3,3,17,19,-6,5,19,18,-8,16,20,21,-18,18,22,20,-17,17,21,23,-20,19,23,22,-19,20,24,25,-22,22,26,24,-21,21,25,27,-24,23,27,26,-23,24,28,29,-26,26,30,28,-25,25,29,31,-28,27,31,30,-27,28,8,9,-30,30,10,8,-29,29,9,11,-32,31,11,10,-31,6,32,33,-5,0,34,32,-7,4,33,35,-2,1,35,34,-1,32,12,13,-34,34,14,12,-33,33,13,15,-36,35,15,14,-35
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *408 {
				a: -0,-7.65461265928025e-07,1,-0,-7.65461265928025e-07,1,-0,-7.65461265928025e-07,1,-0,-7.65461265928025e-07,1,-0.945602357387543,0.325324714183807,0,-0.945602357387543,0.325324714183807,0,-0.945602357387543,0.32532474398613,0,-0.945602357387543,0.325324714183807,0,-0,-7.65461265928025e-07,-1,-0,-7.65461265928025e-07,-1,-0,-7.65461265928025e-07,-1,-0,-7.65461265928025e-07,-1,0.945602357387543,-0.325324833393097,0,0.945602357387543,-0.325324833393097,0,0.945602357387543,-0.325324833393097,0,0.945602357387543,-0.325324833393097,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-0.368635535240173,0.929574072360992,-0,-0.368635535240173,0.929574012756348,-0,-0.368635565042496,0.929574012756348,-0,-0.368635535240173,0.929574012756348,0.784009516239166,0.62074887752533,0,0.784009456634521,0.62074887752533,0,0.784009456634521,0.62074887752533,0,0.784009456634521,0.62074887752533,0,-0.784009575843811,-0.620748698711395,0,-0.784009575843811,-0.620748698711395,0,-0.784009635448456,-0.620748698711395,0,-0.784009575843811,-0.620748698711395,0,-0,0.368638396263123,-0.929572880268097,-0,0.368638396263123,-0.929572880268097,-0,0.3686383664608,-0.929572880268097,-0,0.368638396263123,-0.929572880268097,-0,-0.26255014538765,0.964918375015259,-0,-0.26255014538765,0.964918375015259,-0,-0.26255014538765,0.964918375015259,-0,-0.26255014538765,0.964918375015259,0.910540997982025,-0.413418740034103,0,0.910540997982025,-0.413418740034103,0,0.910540997982025,-0.413418740034103,0,0.910540997982025,-0.413418740034103,0,-0.910540997982025,0.413418740034103,0,-0.910540997982025,0.413418740034103,0,-0.910540997982025,0.413418740034103,0,-0.910540997982025,0.413418740034103,0,-0,0.26255014538765,-0.964918375015259,-0,0.26255014538765,-0.964918375015259,-0,0.26255014538765,-0.964918375015259,-0,0.26255014538765,-0.964918375015259,-0,-0.349768221378326,0.936836242675781,-0,-0.349768221378326,0.936836242675781,-0,-0.349768221378326,0.936836242675781,-0,-0.349768221378326,0.936836242675781,0.799981832504272,0.600024282932281,0,
0.799981832504272,0.600024282932281,0,0.799981832504272,0.600024282932281,0,0.799981832504272,0.600024282932281,0,-0.799981832504272,-0.600024282932281,0,-0.799981832504272,-0.600024282932281,0,-0.799981832504272,-0.600024282932281,0,-0.799981832504272,-0.600024282932281,0,-0,0.349768221378326,-0.936836242675781,-0,0.349768221378326,-0.936836242675781,-0,0.349768221378326,-0.936836242675781,-0,0.349768221378326,-0.936836242675781,-0,-0.103947639465332,0.994582772254944,-0,-0.103947639465332,0.994582772254944,-0,-0.103947639465332,0.994582772254944,-0,-0.103947639465332,0.994582772254944,0.773251593112946,-0.634099423885345,0,0.773251593112946,-0.634099423885345,0,0.773251593112946,-0.634099423885345,0,0.773251593112946,-0.634099423885345,0,-0.773251593112946,0.634099423885345,0,-0.773251593112946,0.634099423885345,0,-0.773251593112946,0.634099423885345,0,-0.773251593112946,0.634099423885345,0,-0,0.103947639465332,-0.994582772254944,-0,0.103947639465332,-0.994582772254944,-0,0.103947639465332,-0.994582772254944,-0,0.103947639465332,-0.994582772254944,-0,-0.162440821528435,0.9867182970047,-0,-0.162440821528435,0.9867182970047,-0,-0.162440821528435,0.9867182970047,-0,-0.162440821528435,0.9867182970047,0.974117338657379,0.226042941212654,0,0.974117338657379,0.226042941212654,0,0.974117338657379,0.226042941212654,0,0.974117338657379,0.226042941212654,0,-0.974117338657379,-0.226042941212654,0,-0.974117338657379,-0.226042941212654,0,-0.974117338657379,-0.226042941212654,0,-0.974117338657379,-0.226042941212654,0,-0,0.162440821528435,-0.9867182970047,-0,0.162440821528435,-0.9867182970047,-0,0.162440821528435,-0.9867182970047,-0,0.162440821528435,-0.9867182970047,-0,-0.180305674672127,-0.9836106300354,-0,-0.180305674672127,-0.9836106300354,-0,-0.180305674672127,-0.9836106300354,-0,-0.180305674672127,-0.9836106300354,0.860878050327301,0.508811414241791,0,0.860878050327301,0.508811414241791,0,0.860878050327301,0.508811414241791,0,0.860878050327301,0.508811414241791,0,-0.860878050327301,-0.508811414241791,0,-0.860878050327301,-0.508811414241791,0,
-0.860878050327301,-0.508811414241791,0,-0.860878050327301,-0.508811414241791,0,-0,0.180305674672127,0.9836106300354,-0,0.180305674672127,0.9836106300354,-0,0.180305674672127,0.9836106300354,-0,0.180305674672127,0.9836106300354,-0,-0.191053822636604,-0.981579542160034,-0,-0.191053822636604,-0.981579542160034,-0,-0.191053822636604,-0.981579542160034,-0,-0.191053822636604,-0.981579542160034,0.784382462501526,-0.620277464389801,0,0.784382462501526,-0.620277464389801,0,0.784382462501526,-0.620277464389801,0,0.784382462501526,-0.620277464389801,0,-0.784382462501526,0.620277464389801,0,-0.784382462501526,0.620277464389801,0,-0.784382462501526,0.620277464389801,0,-0.784382462501526,0.620277464389801,0,-0,0.191053822636604,0.981579542160034,-0,0.191053822636604,0.981579542160034,-0,0.191053822636604,0.981579542160034,-0,0.191053822636604,0.981579542160034
			} 
			NormalsW: *136 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *408 {
				a: -0,1,7.65461265928025e-07,-0,1,7.65461265928025e-07,-0,1,7.65461265928025e-07,-0,1,7.65461265928025e-07,0.325324714183807,0.945602357387543,-0,0.325324714183807,0.945602357387543,-0,0.32532474398613,0.945602357387543,-0,0.325324714183807,0.945602357387543,-0,0,1,-7.65461265928025e-07,0,1,-7.65461265928025e-07,0,1,-7.65461265928025e-07,0,1,-7.65461265928025e-07,0.325324833393097,0.945602357387543,-0,0.325324833393097,0.945602357387543,-0,0.325324833393097,0.945602357387543,-0,0.325324833393097,0.945602357387543,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0.929574072360992,0.368635535240173,-0,0.929574012756348,0.368635535240173,-0,0.929574012756348,0.368635565042496,-0,0.929574012756348,0.368635535240173,-0.62074887752533,0.784009516239166,0,-0.62074887752533,0.784009456634521,0,-0.62074887752533,0.784009456634521,0,-0.62074887752533,0.784009456634521,0,-0.620748698711395,0.784009575843811,-0,-0.620748698711395,0.784009575843811,-0,-0.620748698711395,0.784009635448456,-0,-0.620748698711395,0.784009575843811,-0,0,0.929572880268097,0.368638396263123,0,0.929572880268097,0.368638396263123,0,0.929572880268097,0.3686383664608,0,0.929572880268097,0.368638396263123,-0,0.964918375015259,0.26255014538765,-0,0.964918375015259,0.26255014538765,-0,0.964918375015259,0.26255014538765,-0,0.964918375015259,0.26255014538765,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0.413418740034103,0.910540997982025,-0,0,0.964918375015259,0.26255014538765,0,0.964918375015259,0.26255014538765,0,0.964918375015259,0.26255014538765,0,0.964918375015259,0.26255014538765,-0,0.936836242675781,0.349768221378326,-0,0.936836242675781,0.349768221378326,-0,0.936836242675781,0.349768221378326,-0,0.936836242675781,0.349768221378326,-0.600024282932281,0.799981832504272,0,-0.600024282932281,0.799981832504272,0,
-0.600024282932281,0.799981832504272,0,-0.600024282932281,0.799981832504272,0,-0.600024282932281,0.799981832504272,-0,-0.600024282932281,0.799981832504272,-0,-0.600024282932281,0.799981832504272,-0,-0.600024282932281,0.799981832504272,-0,0,0.936836242675781,0.349768221378326,0,0.936836242675781,0.349768221378326,0,0.936836242675781,0.349768221378326,0,0.936836242675781,0.349768221378326,-0,0.994582772254944,0.103947639465332,-0,0.994582772254944,0.103947639465332,-0,0.994582772254944,0.103947639465332,-0,0.994582772254944,0.103947639465332,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0.634099423885345,0.773251593112946,-0,0,0.994582772254944,0.103947639465332,0,0.994582772254944,0.103947639465332,0,0.994582772254944,0.103947639465332,0,0.994582772254944,0.103947639465332,-0,0.9867182970047,0.162440821528435,-0,0.9867182970047,0.162440821528435,-0,0.9867182970047,0.162440821528435,-0,0.9867182970047,0.162440821528435,-0.226042941212654,0.974117338657379,0,-0.226042941212654,0.974117338657379,0,-0.226042941212654,0.974117338657379,0,-0.226042941212654,0.974117338657379,0,-0.226042941212654,0.974117338657379,-0,-0.226042941212654,0.974117338657379,-0,-0.226042941212654,0.974117338657379,-0,-0.226042941212654,0.974117338657379,-0,0,0.9867182970047,0.162440821528435,0,0.9867182970047,0.162440821528435,0,0.9867182970047,0.162440821528435,0,0.9867182970047,0.162440821528435,0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,-0.508811414241791,0.860878050327301,0,-0.508811414241791,0.860878050327301,0,-0.508811414241791,0.860878050327301,0,-0.508811414241791,0.860878050327301,0,-0.508811414241791,0.860878050327301,-0,-0.508811414241791,0.860878050327301,-0,-0.508811414241791,0.860878050327301,-0,-0.508811414241791,0.860878050327301,-0,
0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,0,0.9836106300354,-0.180305674672127,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0.620277464389801,0.784382462501526,-0,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604,0,0.981579542160034,-0.191053822636604
			} 
			BinormalsW: *136 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *408 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *136 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *272 {
				a: -0.13435959815979,-0.739860475063324,-0.184359550476074,-0.739860475063324,0.0397725328803062,-0.23372095823288,-0.0102274641394615,-0.23372095823288,-0.22491455078125,-0.75959038734436,-0.274913787841797,-0.75959038734436,-0.224914163351059,-0.224334180355072,-0.274914175271988,-0.224334180355072,0.184359550476074,-0.739860057830811,0.13435959815979,-0.739860057830811,0.0102274641394615,-0.233720570802689,-0.0397725328803062,-0.233720570802689,0.274913787841797,-0.743324160575867,0.22491455078125,-0.743324160575867,0.274914175271988,-0.208067923784256,0.224914163351059,-0.208067923784256,-0.0343862771987915,0.27491569519043,0.0156136751174927,0.27491569519043,-0.0343862771987915,0.224916458129883,0.0156136751174927,0.224916458129883,-0.15728485584259,-0.0792198181152344,-0.207284808158875,-0.0792198181152344,-0.15728485584259,-0.0292205810546875,-0.207284808158875,-0.0292205810546875,0.0397725328803062,-0.300172120332718,-0.0102274641394615,-0.300172120332718,-0.132437825202942,-0.066191054880619,-0.182437777519226,-0.066191054880619,0.274914175271988,-0.207928061485291,0.224914163351059,-0.207928061485291,0.18865966796875,0.0694955065846443,0.138660430908203,0.0694955065846443,-0.224914163351059,-0.176890641450882,-0.274914175271988,-0.176890641450882,-0.138660430908203,0.10053289681673,-0.18865966796875,0.10053289681673,0.0102274641394615,-0.318604409694672,-0.0397725328803062,-0.318604409694672,0.182437777519226,-0.0846230685710907,0.132437825202942,-0.0846230685710907,-0.132437825202942,-0.0520544275641441,-0.182437777519226,-0.0520544275641441,0.136715650558472,0.562299728393555,0.0867156982421875,0.562299728393555,0.18865966796875,-0.0695194900035858,0.138660430908203,-0.0695194900035858,0.0273609161376953,0.581523716449738,-0.0226383209228516,0.581523716449738,-0.138660430908203,-0.0901904106140137,-0.18865966796875,-0.0901904106140137,0.0226383209228516,0.560852825641632,-0.0273609161376953,0.560852825641632,0.182437777519226,-0.0651817321777344,0.132437825202942,-0.0651817321777344,
-0.0867156982421875,0.549172401428223,-0.136715650558472,0.549172401428223,0.136715650558472,0.548082530498505,0.0867156982421875,0.548082530498505,-0.133084774017334,0.932046294212341,-0.183084726333618,0.932046294212341,0.0273609161376953,0.379223614931107,-0.0226383209228516,0.379223614931107,-0.106937408447266,0.828872799873352,-0.156936645507812,0.828872799873352,0.0226383209228516,0.409224808216095,-0.0273609161376953,0.409224808216095,0.156936645507812,0.858874022960663,0.106937408447266,0.858874022960663,-0.0867156982421875,0.530594348907471,-0.136715650558472,0.530594348907471,0.183084726333618,0.914558172225952,0.133084774017334,0.914558172225952,-0.133084774017334,0.947535753250122,-0.183084726333618,0.947535753250122,0.149854063987732,1.29444444179535,0.0998541116714478,1.29444444179535,-0.106937408447266,0.639602303504944,-0.156936645507812,0.639602303504944,-0.142997741699219,1.08580815792084,-0.192996978759766,1.08580815792084,0.156936645507812,0.607897400856018,0.106937408447266,0.607897400856018,0.192996978759766,1.05410325527191,0.142997741699219,1.05410325527191,0.183084726333618,0.942338466644287,0.133084774017334,0.942338466644287,-0.0998541116714478,1.28924703598022,-0.149854063987732,1.28924703598022,0.149854063987732,1.29565632343292,0.0998541116714478,1.29565632343292,0.0343862771987915,1.7999552488327,-0.0156136751174927,1.7999552488327,-0.142997741699219,1.21428644657135,-0.192996978759766,1.21428644657135,-0.224916458129883,1.72510886192322,-0.27491569519043,1.72510886192322,0.192996978759766,1.22558867931366,0.142997741699219,1.22558867931366,0.27491569519043,1.73641097545624,0.224916458129883,1.73641097545624,-0.0998541116714478,1.28753447532654,-0.149854063987732,1.28753447532654,0.0156136751174927,1.79183328151703,-0.0343862771987915,1.79183328151703,0.13435959815979,-0.678165912628174,0.184359550476074,-0.678165912628174,-0.207284927368164,-1.26583921909332,-0.15728497505188,-1.26583921909332,0.22491455078125,-0.568565785884857,0.274913787841797,-0.568565785884857,0.118953704833984,
-1.24002182483673,0.168952941894531,-1.24002182483673,-0.274913787841797,-0.543125212192535,-0.22491455078125,-0.543125212192535,-0.168952941894531,-1.21458125114441,-0.118953704833984,-1.21458125114441,-0.184359550476074,-0.687181055545807,-0.13435959815979,-0.687181055545807,0.15728497505188,-1.2748544216156,0.207284927368164,-1.2748544216156,-0.207284927368164,-1.26134657859802,-0.15728497505188,-1.26134657859802,0.15728485584259,-1.73102116584778,0.207284808158875,-1.73102116584778,0.118953704833984,-0.905165135860443,0.168952941894531,-0.905165135860443,0.0292205810546875,-1.49291789531708,0.0792198181152344,-1.49291789531708,-0.168952941894531,-0.936178982257843,-0.118953704833984,-0.936178982257843,-0.0792198181152344,-1.52393174171448,-0.0292205810546875,-1.52393174171448,0.15728497505188,-1.2708991765976,0.207284927368164,-1.2708991765976,-0.207284808158875,-1.74057376384735,-0.15728485584259,-1.74057376384735
			} 
			UVIndex: *136 {
				a: 0,2,3,1,4,6,7,5,8,10,11,9,12,14,15,13,16,18,19,17,20,22,23,21,24,26,27,25,28,30,31,29,32,34,35,33,36,38,39,37,40,42,43,41,44,46,47,45,48,50,51,49,52,54,55,53,56,58,59,57,60,62,63,61,64,66,67,65,68,70,71,69,72,74,75,73,76,78,79,77,80,82,83,81,84,86,87,85,88,90,91,89,92,94,95,93,96,98,99,97,100,102,103,101,104,106,107,105,108,110,111,109,112,114,115,113,116,118,119,117,120,122,123,121,124,126,127,125,128,130,131,129,132,134,135,133
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2432296021088, "Model::Crack", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1.66666650772095,1.66666650772095,1.66666650772095
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2430708909696, "Material::White_Light", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Crack, Model::RootNode
	C: "OO",2432296021088,0
	
	;Material::White_Light, Model::Crack
	C: "OO",2430708909696,2432296021088
	
	;Geometry::Scene, Model::Crack
	C: "OO",2423290377696,2432296021088
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
