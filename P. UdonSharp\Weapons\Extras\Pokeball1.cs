using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class Pokeball1 : UdonSharpBehaviour
{
    public VRC_Pickup pickup;

    //Actual Script
    private VRCPlayerApi localPlayer;  
    public GameObject RadiusObject;
    public Rigidbody rb;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        RadiusObject.SetActive(false);

        rb = GetComponent<Rigidbody>();
        pickup = GetComponent<VRC_Pickup>();
    }

    public override void OnPickup(){if (pickup.currentPlayer == Networking.LocalPlayer){if (!Networking.IsOwner(gameObject)){Networking.SetOwner(Networking.LocalPlayer, gameObject);}}}

    public override void OnDrop(){
        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(CustomUpdate));
    }

    public void CustomUpdate(){
        RadiusObject.SetActive(true);
        pickup.pickupable = false;
        SendCustomEventDelayedSeconds(nameof(Allowpickup), 30f);
    }
    public void Allowpickup(){
        pickup.pickupable = true;
    }
    public void CustomUpdate2(){
        RadiusObject.SetActive(false);
    }

    //when collision happens
    public void OnCollisionEnter(Collision collision){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(CustomUpdate2));}
}