﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class RandomColorContinuous : UdonSharpBehaviour
{
    public MeshRenderer targetRenderer;

    void Start()
    {
        if (targetRenderer == null){targetRenderer = GetComponent<MeshRenderer>();}
        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.3f);
    }

    public void CustomUpdate()
    {
        if (targetRenderer != null){targetRenderer.material.color = new Color(Random.value, Random.value, Random.value);}
        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.3f);
    }
}
