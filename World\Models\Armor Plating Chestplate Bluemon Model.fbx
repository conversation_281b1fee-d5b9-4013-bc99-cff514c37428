; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 21
		Hour: 0
		Minute: 35
		Second: 50
		Millisecond: 337
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\lr2e1o8r_Armor Plating Chestplate Bluemon Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\lr2e1o8r_Armor Plating Chestplate Bluemon Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2410575218848, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2404204117440, "Geometry::Scene", "Mesh" {
		Vertices: *117 {
			a: -29.9999982118607,2.06169188022614,51.9615292549133,-60.0000023841858,2.06169188022614,0,-29.9999237060547,15.1082992553711,51.9622802734375,-60.0000023841858,22.0616936683655,0,-36.6683959960938,-22.0617294311523,0,-18.3338165283203,-22.0617294311523,31.7535400390625,2.48352693965614e-07,22.0616936683655,-1.49011611938477e-06,30.0000071525574,2.06169188022614,51.9615232944489,29.9999237060547,15.1082992553711,51.9622802734375,18.3338165283203,-22.0617294311523,31.7535400390625,60.0000023841858,2.06169188022614,-5.96046447753906e-06,60.0000023841858,22.0616936683655,-5.96046447753906e-06,36.6683959960938,-22.0617294311523,0,29.9999952316284,2.06169188022614,-51.9615292549133,29.9999952316284,22.0616936683655,-51.9615292549133,18.3338165283203,-22.0617294311523,-31.7535400390625,-29.9999952316284,2.06169188022614,-51.9615292549133,-29.9999952316284,22.0616936683655,-51.9615292549133,-18.3338165283203,-22.0617294311523,-31.7535400390625,-65.9999847412109,2.06165313720703,0,-32.9998016357422,2.06165313720703,57.159423828125,-40.3350830078125,-22.0617294311523,0,-20.1671600341797,-22.0617294311523,34.930419921875,32.9998016357422,2.06165313720703,57.159423828125,20.1671600341797,-22.0617294311523,34.930419921875,65.9999847412109,2.06165313720703,0,40.3354644775391,-22.0617294311523,0,32.9998016357422,2.06165313720703,-57.159423828125,20.1671600341797,-22.0617294311523,-34.930419921875,29.9999237060547,41.8411254882812,-51.9622802734375,59.9998474121094,30.1296234130859,0,-0,41.8411254882812,0,-32.9998016357422,2.06165313720703,-57.159423828125,-20.1671600341797,-22.0617294311523,-34.930419921875,-29.9999237060547,65.1578903198242,-51.9622802734375,29.9999237060547,65.1578903198242,-51.9622802734375,-0,65.1578903198242,0,-59.9998474121094,30.1296234130859,0,-29.9999237060547,41.8411254882812,-51.9622802734375
		} 
		PolygonVertexIndex: *234 {
			a: 4,6,-6,5,6,-10,9,6,-13,12,6,-16,15,6,-19,18,6,-5,2,6,-4,8,6,-3,11,6,-9,29,31,-31,34,36,-36,37,31,-39,0,3,-2,3,0,-3,7,2,-1,2,7,-9,10,8,-8,8,10,-12,13,11,-11,11,13,-15,16,14,-14,14,16,-18,1,17,-17,17,1,-4,19,22,-21,22,19,-22,20,24,-24,24,20,-23,23,26,-26,26,23,-25,25,28,-28,28,25,-27,27,33,-33,33,27,-29,32,21,-20,21,32,-34,0,23,-8,23,0,-21,5,21,-5,21,5,-23,9,22,-6,22,9,-25,7,25,-11,25,7,-24,12,24,-10,24,12,-27,10,27,-14,27,10,-26,15,26,-13,26,15,-29,13,32,-17,32,13,-28,18,28,-16,28,18,-34,16,19,-2,19,16,-33,4,33,-19,33,4,-22,1,20,-1,20,1,-20,3,38,-18,38,3,-38,17,31,-7,31,17,-39,6,37,-4,37,6,-32,14,30,-12,30,14,-30,11,31,-7,31,11,-31,6,29,-15,29,6,-32,17,35,-15,35,17,-35,14,36,-7,36,14,-36,6,34,-18,34,6,-37
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *702 {
				a: 0.769087433815002,-0.639143586158752,0,-1.70066378757383e-08,-1,-8.50331893786915e-09,0.384536594152451,-0.639121770858765,-0.666074335575104,0.384536594152451,-0.639121770858765,-0.666074335575104,-1.70066378757383e-08,-1,-8.50331893786915e-09,-0.384536594152451,-0.639121770858765,-0.666074335575104,-0.384536594152451,-0.639121770858765,-0.666074335575104,-1.70066378757383e-08,-1,-8.50331893786915e-09,-0.769087433815002,-0.639143586158752,1.63043551992814e-08,-0.769087433815002,-0.639143586158752,1.63043551992814e-08,-1.70066378757383e-08,-1,-8.50331893786915e-09,-0.384536623954773,-0.639121770858765,0.666074335575104,-0.384536623954773,-0.639121770858765,0.666074335575104,-1.70066378757383e-08,-1,-8.50331893786915e-09,0.384536594152451,-0.639121770858765,0.666074335575104,0.384536594152451,-0.639121770858765,0.666074335575104,-1.70066378757383e-08,-1,-8.50331893786915e-09,0.769087433815002,-0.639143586158752,0,-0,0.991165101528168,0.132633939385414,-0,0.991165101528168,0.132633939385414,-0,0.991165101528168,0.132633939385414,-0,0.991165101528168,0.132633939385414,-0,0.991165101528168,0.132633939385414,-0,0.991165101528168,0.132633939385414,9.88199744256235e-09,0.991165101528168,0.132633924484253,9.88199744256235e-09,0.991165101528168,0.132633924484253,9.88199744256235e-09,0.991165101528168,0.132633924484253,0.190415591001511,0.975528717041016,0.109934613108635,0.190415591001511,0.975528717041016,0.109934613108635,0.190415591001511,0.975528717041016,0.109934613108635,-0,1,0,-0,1,0,-0,1,0,-0.190415591001511,0.975528717041016,0.109934613108635,-0.190415591001511,0.975528717041016,0.109934613108635,-0.190415591001511,0.975528717041016,0.109934613108635,-0.5,-4.71286439278629e-05,0.866025447845459,-1,0,-2.61536570178578e-06,-1,-6.88099726176006e-06,-1.32489014958992e-06,-1,0,-2.61536570178578e-06,-0.5,-4.71286439278629e-05,0.866025447845459,-0.500001132488251,-4.01157813030295e-05,0.866024732589722,0.500001788139343,-4.02740661229473e-05,0.866024374961853,-0.500001132488251,-4.01157813030295e-05,0.866024732589722,
-0.5,-4.71286439278629e-05,0.866025447845459,-0.500001132488251,-4.01157813030295e-05,0.866024732589722,0.500001788139343,-4.02740661229473e-05,0.866024374961853,0.500003457069397,-4.69177430204581e-05,0.866023421287537,1,0,-1.20444653362028e-07,0.500003457069397,-4.69177430204581e-05,0.866023421287537,0.500001788139343,-4.02740661229473e-05,0.866024374961853,0.500003457069397,-4.69177430204581e-05,0.866023421287537,1,0,-1.20444653362028e-07,1,-6.77549223837559e-06,-2.08196934181615e-06,0.499999940395355,0,-0.866025447845459,1,-6.77549223837559e-06,-2.08196934181615e-06,1,0,-1.20444653362028e-07,1,-6.77549223837559e-06,-2.08196934181615e-06,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,-0.5,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,0.499999940395355,0,-0.866025447845459,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-1,-6.88099726176006e-06,-1.32489014958992e-06,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-0.5,0,-0.866025447845459,-1,-6.88099726176006e-06,-1.32489014958992e-06,-1,0,-2.61536570178578e-06,-0.636903524398804,-0.677602589130402,0.367707222700119,-0.636872828006744,-0.677627801895142,0.367713928222656,-0.636888146400452,-0.677615165710449,0.367710590362549,-0.636872828006744,-0.677627801895142,0.367713928222656,-0.636903524398804,-0.677602589130402,0.367707222700119,-0.636888146400452,-0.677615165710449,0.367710590362549,-0,-0.677642345428467,0.735391676425934,-0,-0.677642285823822,0.735391676425934,-0,-0.677642345428467,0.735391676425934,-0,-0.677642285823822,0.735391676425934,-0,-0.677642345428467,0.735391676425934,-0,-0.677642345428467,0.735391676425934,0.636888265609741,-0.677621722221375,0.367698431015015,0.636900544166565,-0.677589356899261,0.367736876010895,0.636894404888153,-0.677605509757996,0.367717653512955,0.636900544166565,-0.677589356899261,0.367736876010895,0.636888265609741,-0.677621722221375,0.367698431015015,0.636894404888153,-0.677605509757996,0.367717653512955,0.636907875537872,-0.677597165107727,-0.3677097260952,
0.636868417263031,-0.677629590034485,-0.36771833896637,0.636888146400452,-0.677613377571106,-0.367714047431946,0.636868417263031,-0.677629590034485,-0.36771833896637,0.636907875537872,-0.677597165107727,-0.3677097260952,0.636888146400452,-0.677613377571106,-0.367714047431946,-0,-0.677642345428467,-0.735391676425934,-0,-0.677642285823822,-0.735391676425934,-0,-0.677642345428467,-0.735391676425934,-0,-0.677642285823822,-0.735391676425934,-0,-0.677642345428467,-0.735391676425934,-0,-0.677642345428467,-0.735391676425934,-0.636888265609741,-0.677621722221375,-0.367698431015015,-0.636897802352905,-0.677596509456635,-0.367728352546692,-0.636893033981323,-0.677609086036682,-0.367713391780853,-0.636897802352905,-0.677596509456635,-0.367728352546692,-0.636888265609741,-0.677621722221375,-0.367698431015015,-0.636893033981323,-0.677609086036682,-0.367713391780853,7.40448501124202e-13,1,7.45359875509166e-06,-0,1,7.45358966014464e-06,3.70224250562101e-13,1,7.45359420761815e-06,-0,1,7.45358966014464e-06,7.40448501124202e-13,1,7.45359875509166e-06,3.70224250562101e-13,1,7.45359420761815e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,6.45630598228308e-06,1,3.72754902855377e-06,6.45718500891235e-06,1,3.72796421288513e-06,6.45674572297139e-06,1,3.72775662071945e-06,6.45718500891235e-06,1,3.72796421288513e-06,6.45630598228308e-06,1,3.72754902855377e-06,6.45674572297139e-06,1,3.72775662071945e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,6.45719273961731e-06,1,-3.72806312043394e-06,6.45637646812247e-06,1,-3.72749741472944e-06,6.45678483124357e-06,1,-3.72778026758169e-06,6.45637646812247e-06,1,-3.72749741472944e-06,6.45719273961731e-06,1,-3.72806312043394e-06,6.45678483124357e-06,1,-3.72778026758169e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,-7.45359830034431e-06,-0,1,-7.45359875509166e-06,-0,1,-7.45359830034431e-06,-0,1,-7.45359875509166e-06,-0,1,-7.45359830034431e-06,-0,1,-7.45359830034431e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-6.45629552309401e-06,1,-3.72754448108026e-06,
-6.45718864689115e-06,1,-3.72796625924821e-06,-6.45674208499258e-06,1,-3.72775548385107e-06,-6.45718864689115e-06,1,-3.72796625924821e-06,-6.45629552309401e-06,1,-3.72754448108026e-06,-6.45674208499258e-06,1,-3.72775548385107e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-6.45718864689115e-06,1,3.72805993720249e-06,-6.45638010610128e-06,1,3.72749946109252e-06,-6.45678437649622e-06,1,3.72777958546067e-06,-6.45638010610128e-06,1,3.72749946109252e-06,-6.45718864689115e-06,1,3.72805993720249e-06,-6.45678437649622e-06,1,3.72777958546067e-06,-0.866025388240814,1.66349673236255e-05,-0.500000059604645,-0.866027534008026,-1.58530001499457e-05,-0.499996274709702,-0.86602646112442,3.90983586839866e-07,-0.499998152256012,-0.866027534008026,-1.58530001499457e-05,-0.499996274709702,-0.866025388240814,1.66349673236255e-05,-0.500000059604645,-0.86602646112442,3.90983586839866e-07,-0.499998152256012,0.866025447845459,-2.21165173570625e-05,-0.499999910593033,0.866029143333435,4.85417963602686e-08,-0.499993592500687,0.866027295589447,-1.10339879029198e-05,-0.49999675154686,0.866029143333435,4.85417963602686e-08,-0.499993592500687,0.866025447845459,-2.21165173570625e-05,-0.499999910593033,0.866027295589447,-1.10339879029198e-05,-0.49999675154686,2.48352680642938e-08,-7.53366506955899e-08,1,-0,0,1,1.24176340321469e-08,-3.7668325347795e-08,1,-0,0,1,2.48352680642938e-08,-7.53366506955899e-08,1,1.24176340321469e-08,-3.7668325347795e-08,1,0.866025328636169,-1.5853154764045e-05,-0.50000011920929,0.866030752658844,1.70044550031889e-05,-0.499990701675415,0.866028070449829,5.75650119571947e-07,-0.499995410442352,0.866030752658844,1.70044550031889e-05,-0.499990701675415,0.866025328636169,-1.5853154764045e-05,-0.50000011920929,0.866028070449829,5.75650119571947e-07,-0.499995410442352,7.45058059692383e-08,-7.38783398901433e-07,1,-1.47051260057651e-08,-7.53366506955899e-08,1,2.99003417580934e-08,-4.0706001414037e-07,1,-1.47051260057651e-08,-7.53366506955899e-08,1,7.45058059692383e-08,-7.38783398901433e-07,1,2.99003417580934e-08,-4.0706001414037e-07,1,
-0.866025447845459,2.67944066933978e-08,-0.499999910593033,-0.86602908372879,-2.21162918023765e-05,-0.499993592500687,-0.866027235984802,-1.10447490442311e-05,-0.49999675154686,-0.86602908372879,-2.21162918023765e-05,-0.499993592500687,-0.866025447845459,2.67944066933978e-08,-0.499999910593033,-0.866027235984802,-1.10447490442311e-05,-0.49999675154686,-0,-1.74265624082182e-05,-1,-0,-1.74265624082182e-05,-1,-0,-1.74265624082182e-05,-1,-0,-1.74265624082182e-05,-1,-0,-1.74265624082182e-05,-1,-0,-1.74265624082182e-05,-1,0.866025447845459,1.01505975180771e-05,0.49999988079071,0.866029143333435,-1.22973231597712e-08,0.499993562698364,0.866027295589447,5.06915012010722e-06,0.499996721744537,0.866029143333435,-1.22973231597712e-08,0.499993562698364,0.866025447845459,1.01505975180771e-05,0.49999988079071,0.866027295589447,5.06915012010722e-06,0.499996721744537,-0.866025447845459,-2.22789413584223e-08,0.49999988079071,-0.866029143333435,1.01504947451758e-05,0.499993592500687,-0.866027295589447,5.0641078814806e-06,0.499996721744537,-0.866029143333435,1.01504947451758e-05,0.499993592500687,-0.866025447845459,-2.22789413584223e-08,0.49999988079071,-0.866027295589447,5.0641078814806e-06,0.499996721744537
			} 
			NormalsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *702 {
				a: 0.599614918231964,0.721522212028503,-0.346219211816788,0.866005778312683,-1.04758974828201e-08,-0.500034034252167,0.599587082862854,0.721552968025208,-0.346203178167343,-1.11011395631522e-08,0.721554815769196,-0.692357301712036,2.8595955378772e-24,8.50331893786915e-09,-1,1.11011395631522e-08,0.721554815769196,-0.692357301712036,-0.599587082862854,0.721552968025208,-0.346203178167343,-0.866005778312683,1.89797955130189e-08,-0.500034034252167,-0.599614918231964,0.721522212028503,-0.346219211816788,-0.599614918231964,0.721522212028503,0.346219211816788,-0.866005778312683,1.04758974828201e-08,0.500034034252167,-0.599587082862854,0.721553027629852,0.346203178167343,-7.94617349697546e-09,0.72155487537384,0.692357301712036,-2.8595955378772e-24,-8.50331893786915e-09,1,-1.11011395631522e-08,0.721554815769196,0.692357301712036,0.599587082862854,0.721552968025208,0.346203178167343,0.866005778312683,-1.89797955130189e-08,0.500034034252167,0.599614918231964,0.721522212028503,0.346219211816788,0,-0.132633939385414,0.991165101528168,0,-0.132633939385414,0.991165101528168,0,-0.132633939385414,0.991165101528168,0,-0.132633939385414,0.991165101528168,0,-0.132633939385414,0.991165101528168,0,-0.132633939385414,0.991165101528168,-6.58257781616101e-10,-0.132633924484253,0.991165101528168,-6.58257781616101e-10,-0.132633924484253,0.991165101528168,-6.58257781616101e-10,-0.132633924484253,0.991165101528168,-0.0210609212517738,-0.107898369431496,0.993938863277435,-0.0210609212517738,-0.107898369431496,0.993938863277435,-0.0210609212517738,-0.107898369431496,0.993938863277435,0,-0,1,0,-0,1,0,-0,1,0.0210609212517738,-0.107898369431496,0.993938863277435,0.0210609212517738,-0.107898369431496,0.993938863277435,0.0210609212517738,-0.107898369431496,0.993938863277435,-4.71286439278629e-05,1.00000011920929,2.72097367997048e-05,0,1,-0,-6.88100271872827e-06,1,3.9727251532895e-06,0,1,-0,-4.71286439278629e-05,1.00000011920929,2.72097367997048e-05,-4.01158722524997e-05,1,2.31607718887972e-05,4.97089435586018e-12,1,4.65045413875487e-05,
-9.5726898630133e-13,1,4.63217475044075e-05,2.87528616367549e-12,1.00000011920929,5.44194735994097e-05,-9.5726898630133e-13,1,4.63217475044075e-05,4.97089435586018e-12,1,4.65045413875487e-05,1.42903917865755e-12,1,5.41760673513636e-05,0,1,0,4.69180631625932e-05,1,2.70876607828541e-05,4.02742080041207e-05,1,2.32521051657386e-05,4.69180631625932e-05,1,2.70876607828541e-05,0,1,0,6.77550042382791e-06,1,3.91180037695449e-06,0,1,0,6.77548405292328e-06,1,-3.91182857129024e-06,0,1,0,6.77548405292328e-06,1,-3.91182857129024e-06,0,1,0,0,1,0,0,1.00000011920929,-0,0,1,0,0,1,0,0,1,0,0,1.00000011920929,-0,0,1.00000011920929,-0,-6.88099180479185e-06,1,-3.97274379793089e-06,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,-6.88099180479185e-06,1,-3.97274379793089e-06,0,1,-0,0.251837372779846,0.267930150032043,0.929941654205322,0.251841992139816,0.26793497800827,0.929938971996307,0.251839697360992,0.267932564020157,0.929940283298492,0.251841992139816,0.26793497800827,0.929938971996307,0.251837372779846,0.267930150032043,0.929941654205322,0.251839697360992,0.267932564020157,0.929940283298492,-0,0.735391676425934,0.677642345428467,-0,0.735391676425934,0.677642285823822,-0,0.735391676425934,0.677642345428467,-0,0.735391676425934,0.677642285823822,-0,0.735391676425934,0.677642345428467,-0,0.735391676425934,0.677642345428467,-0.251824349164963,0.267930299043655,0.929945111274719,-0.251850664615631,0.2679583132267,0.929929912090302,-0.251837491989136,0.267944306135178,0.929937481880188,-0.251850664615631,0.2679583132267,0.929929912090302,-0.251824349164963,0.267930299043655,0.929945111274719,-0.251837491989136,0.267944306135178,0.929937481880188,0.251841068267822,-0.267930060625076,0.929940640926361,0.251846939325333,-0.267936408519745,0.929937243461609,0.251844018697739,-0.267933249473572,0.929938971996307,0.251846939325333,-0.267936408519745,0.929937243461609,0.251841068267822,-0.267930060625076,0.929940640926361,0.251844018697739,-0.267933249473572,0.929938971996307,0,0.735391676425934,-0.677642345428467,0,0.735391676425934,-0.677642285823822,
0,0.735391676425934,-0.677642345428467,0,0.735391676425934,-0.677642285823822,0,0.735391676425934,-0.677642345428467,0,0.735391676425934,-0.677642345428467,-0.251824349164963,-0.267930299043655,0.929945111274719,-0.251844823360443,-0.267952114343643,0.929933249950409,-0.251834571361542,-0.267941206693649,0.929939150810242,-0.251844823360443,-0.267952114343643,0.929933249950409,-0.251824349164963,-0.267930299043655,0.929945111274719,-0.251834571361542,-0.267941206693649,0.929939150810242,-5.51900612241607e-18,-7.45359875509166e-06,1,0,-7.45358966014464e-06,1,-2.75950140684681e-18,-7.45359420761815e-06,1,0,-7.45358966014464e-06,1,-5.51900612241607e-18,-7.45359875509166e-06,1,-2.75950140684681e-18,-7.45359420761815e-06,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-2.4066196951944e-11,-3.72754902855377e-06,1,-2.39537313595495e-11,-3.72796421288513e-06,1,-2.40099641557467e-11,-3.72775662071945e-06,1,-2.39537313595495e-11,-3.72796421288513e-06,1,-2.4066196951944e-11,-3.72754902855377e-06,1,-2.40099641557467e-11,-3.72775662071945e-06,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,2.40136105444932e-11,3.72806312043394e-06,1,2.40661258282815e-11,3.72749741472944e-06,1,2.40398673190256e-11,3.72778026758169e-06,1,2.40661258282815e-11,3.72749741472944e-06,1,2.40136105444932e-11,3.72806312043394e-06,1,2.40398673190256e-11,3.72778026758169e-06,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,7.45359830034431e-06,1,0,7.45359875509166e-06,1,0,7.45359830034431e-06,1,0,7.45359875509166e-06,1,0,7.45359830034431e-06,1,0,7.45359830034431e-06,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-2.4066127563005e-11,3.72754448108026e-06,1,-2.40721817479361e-11,3.72796625924821e-06,1,-2.4069156390194e-11,3.72775548385107e-06,1,-2.40721817479361e-11,3.72796625924821e-06,1,-2.4066127563005e-11,3.72754448108026e-06,1,-2.4069156390194e-11,3.72775548385107e-06,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,2.40727871664292e-11,-3.72805993720249e-06,1,2.40661518491336e-11,-3.72749946109252e-06,1,2.40694686404197e-11,-3.72777958546067e-06,1,
2.40661518491336e-11,-3.72749946109252e-06,1,2.40727871664292e-11,-3.72805993720249e-06,1,2.40694686404197e-11,-3.72777958546067e-06,1,1.44063042171183e-05,1.00000011920929,8.31748639029684e-06,-1.37291353894398e-05,1,-7.92644095781725e-06,3.38602120564246e-07,1,1.95491068666342e-07,-1.37291353894398e-05,1,-7.92644095781725e-06,1.44063042171183e-05,1.00000011920929,8.31748639029684e-06,3.38602120564246e-07,1,1.95491068666342e-07,1.91534672921989e-05,1,-1.10582568595419e-05,-4.20386108146431e-08,1.00000011920929,2.42705873176874e-08,9.55573523242492e-06,1,-5.51695802641916e-06,-4.20386108146431e-08,1.00000011920929,2.42705873176874e-08,1.91534672921989e-05,1,-1.10582568595419e-05,9.55573523242492e-06,1,-5.51695802641916e-06,1.87100599499234e-15,1,7.53366506955899e-08,0,1,-0,4.67751445808526e-16,1,3.7668325347795e-08,0,1,-0,1.87100599499234e-15,1,7.53366506955899e-08,4.67751445808526e-16,1,3.7668325347795e-08,1.37292336148676e-05,1,-7.92657920101192e-06,-1.4744407053513e-05,1,8.47084902488859e-06,-5.07541813021817e-07,1,2.72211877927475e-07,-1.4744407053513e-05,1,8.47084902488859e-06,1.37292336148676e-05,1,-7.92657920101192e-06,-5.07541813021817e-07,1,2.72211877927475e-07,5.50436534188774e-14,1,7.38783398901433e-07,-1.10783492110949e-15,1,7.53366506955899e-08,1.21712307184327e-14,1,4.0706001414037e-07,-1.10783492110949e-15,1,7.53366506955899e-08,5.50436534188774e-14,1,7.38783398901433e-07,1.21712307184327e-14,1,4.0706001414037e-07,2.32046399872843e-08,1,1.33972006821637e-08,-1.91533526958665e-05,1,-1.10580040200148e-05,-9.56505391513929e-06,1,-5.5223381423275e-06,-1.91533526958665e-05,1,-1.10580040200148e-05,2.32046399872843e-08,1,1.33972006821637e-08,-9.56505391513929e-06,1,-5.5223381423275e-06,0,1,-1.74265624082182e-05,0,1,-1.74265624082182e-05,0,1,-1.74265624082182e-05,0,1,-1.74265624082182e-05,0,1,-1.74265624082182e-05,0,1,-1.74265624082182e-05,-8.79067647474585e-06,1,-5.07529830429121e-06,1.0649840120891e-08,1,6.14858253200623e-09,-4.39002224084106e-06,1,-2.5345584617753e-06,1.0649840120891e-08,1,6.14858253200623e-09,
-8.79067647474585e-06,1,-5.07529830429121e-06,-4.39002224084106e-06,1,-2.5345584617753e-06,-1.92941307375349e-08,1,1.11394689028543e-08,8.79062463354785e-06,1.00000011920929,-5.07518234371673e-06,4.38565575677785e-06,1,-2.53203756983567e-06,8.79062463354785e-06,1.00000011920929,-5.07518234371673e-06,-1.92941307375349e-08,1,1.11394689028543e-08,4.38565575677785e-06,1,-2.53203756983567e-06
			} 
			BinormalsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *702 {
				a: -0.221283793449402,-0.266272842884064,-0.938153684139252,-0.500034034252167,1.58678208350693e-08,-0.866005778312683,-0.701873898506165,0.266241788864136,-0.660672605037689,-0.923109710216522,-0.26623672246933,-0.277464240789413,-1,1.70066378757383e-08,1.44612863058653e-16,-0.923109710216522,0.26623672246933,0.277464240789413,-0.701873898506165,-0.266241788864136,0.660672605037689,-0.500034034252167,1.13997478123906e-09,0.866005778312683,-0.221283793449402,0.266272842884064,0.938153684139252,0.221283793449402,-0.266272842884064,0.938153684139252,0.500034034252167,-1.58678208350693e-08,0.866005778312683,0.701873898506165,0.266241788864136,0.660672605037689,0.923109710216522,-0.26623672246933,0.277464270591736,1,-1.70066378757383e-08,-1.44612863058653e-16,0.923109710216522,0.26623672246933,-0.277464240789413,0.701873898506165,-0.266241788864136,-0.660672605037689,0.500034034252167,-1.13997478123906e-09,-0.866005778312683,0.221283793449402,0.266272842884064,-0.938153684139252,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,9.88199744256235e-09,6.58245846718586e-10,-1,9.88199744256235e-09,6.58245846718586e-10,-1,9.88199744256235e-09,6.58245846718586e-10,-0.981477677822113,0.191576778888702,-1.63815339071505e-09,-0.981477677822113,0.191576778888702,-1.63815339071505e-09,-0.981477677822113,0.191576778888702,-1.63815339071505e-09,-1,0,0,-1,0,0,-1,0,0,-0.981477677822113,-0.191576778888702,1.63815339071505e-09,-0.981477677822113,-0.191576778888702,1.63815339071505e-09,-0.981477677822113,-0.191576778888702,1.63815339071505e-09,0.866025447845459,2.72097349807154e-05,0.499999970197678,-2.61536570178578e-06,0,1,-1.34209653879225e-06,-3.97273424823652e-06,1,-2.61536570178578e-06,0,1,0.866025447845459,2.72097349807154e-05,0.499999970197678,0.866024792194366,2.31609265028965e-05,0.500001132488251,0.866024374961853,2.32523507293081e-05,-0.500001847743988,0.866024672985077,-2.31609319598647e-05,0.50000125169754,0.866025447845459,-2.72097404376836e-05,0.500000059604645,0.866024672985077,-2.31609319598647e-05,0.50000125169754,
0.866024374961853,2.32523507293081e-05,-0.500001847743988,0.866023421287537,2.7088219212601e-05,-0.500003457069397,-1.37651028353503e-07,0,-1,0.866023421287537,-2.7088219212601e-05,-0.500003397464752,0.866024315357208,-2.32523470913293e-05,-0.500001847743988,0.866023421287537,-2.7088219212601e-05,-0.500003397464752,-1.37651028353503e-07,0,-1,-2.09917584470531e-06,3.91181447412237e-06,-1,-0.866025447845459,0,-0.499999970197678,-2.06476306630066e-06,-3.91181447412237e-06,-1,-1.37651028353503e-07,0,-1,-2.06476306630066e-06,-3.91181447412237e-06,-1,-0.866025447845459,0,-0.499999970197678,-0.866025447845459,0,-0.499999970197678,-0.866025447845459,0,0.500000059604645,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,-0.499999940395355,-0.866025447845459,0,0.500000059604645,-0.866025447845459,0,0.500000059604645,-1.30768387407443e-06,3.97273470298387e-06,1,-0.866025447845459,0,0.499999970197678,-0.866025447845459,0,0.499999970197678,-0.866025447845459,0,0.499999970197678,-1.30768387407443e-06,3.97273470298387e-06,1,-2.61536570178578e-06,0,1,0.728650748729706,-0.684885561466217,0,0.728675961494446,-0.684858679771423,-1.4617818123952e-05,0.728663325309753,-0.684872150421143,-7.31987620383734e-06,0.728675961494446,-0.684858679771423,-1.4617818123952e-05,0.728650748729706,-0.684885561466217,0,0.728663325309753,-0.684872150421143,-7.31987620383734e-06,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.728668570518494,0.684866547584534,1.06080322304081e-09,0.728648722171783,0.684887588024139,-1.14565036710701e-05,0.728658616542816,0.684877097606659,-5.74967907596147e-06,0.728648722171783,0.684887588024139,-1.14565036710701e-05,0.728668570518494,0.684866547584534,1.06080322304081e-09,0.728658616542816,0.684877097606659,-5.74967907596147e-06,0.728645622730255,0.684890925884247,-1.09586038021803e-08,0.728678107261658,0.684856414794922,-1.86900324479211e-05,0.728661894798279,0.684873700141907,-9.35051048145397e-06,0.728678107261658,0.684856414794922,-1.86900324479211e-05,0.728645622730255,0.684890925884247,-1.09586038021803e-08,
0.728661894798279,0.684873700141907,-9.35051048145397e-06,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.728668570518494,-0.684866547584534,1.06080322304081e-09,0.728653132915497,-0.684882938861847,-8.93937885848572e-06,0.728660821914673,-0.684874713420868,-4.47466254627216e-06,0.728653132915497,-0.684882938861847,-8.93937885848572e-06,0.728668570518494,-0.684866547584534,1.06080322304081e-09,0.728660821914673,-0.684874713420868,-4.47466254627216e-06,-1,7.40448501124202e-13,0,-1,0,0,-1,3.70224250562101e-13,0,-1,0,0,-1,7.40448501124202e-13,0,-1,3.70224250562101e-13,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,6.45630598228308e-06,0,-1,6.45718500891235e-06,1.18422402418071e-13,-1,6.45674572297139e-06,5.92120414657192e-14,-1,6.45718500891235e-06,1.18422402418071e-13,-1,6.45630598228308e-06,0,-1,6.45674572297139e-06,5.92120414657192e-14,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,6.45719273961731e-06,-5.9212109228355e-14,-1,6.45637646812247e-06,0,-1,6.45678483124357e-06,-2.96077452919402e-14,-1,6.45637646812247e-06,0,-1,6.45719273961731e-06,-5.9212109228355e-14,-1,6.45678483124357e-06,-2.96077452919402e-14,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,-6.45629552309401e-06,1.69509097808271e-18,-1,-6.45718864689115e-06,0,-1,-6.45674208499258e-06,0,-1,-6.45718864689115e-06,0,-1,-6.45629552309401e-06,1.69509097808271e-18,-1,-6.45674208499258e-06,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,-6.45718864689115e-06,1.69532538038879e-18,-1,-6.45638010610128e-06,-1.69507050536255e-18,-1,-6.45678437649622e-06,0,-1,-6.45638010610128e-06,-1.69507050536255e-18,-1,-6.45718864689115e-06,1.69532538038879e-18,-1,-6.45678437649622e-06,0,-0.50000011920929,-4.95760659087463e-13,0.866025388240814,-0.499996304512024,-4.72456221280865e-13,0.866027593612671,-0.499998152256012,0,0.86602646112442,-0.499996304512024,-4.72456221280865e-13,0.866027593612671,-0.50000011920929,-4.95760659087463e-13,0.866025388240814,-0.499998152256012,0,0.86602646112442,-0.499999910593033,0,-0.866025507450104,
-0.499993592500687,0,-0.866029143333435,-0.49999675154686,3.28838464704745e-13,-0.866027295589447,-0.499993592500687,0,-0.866029143333435,-0.499999910593033,0,-0.866025507450104,-0.49999675154686,3.28838464704745e-13,-0.866027295589447,1,0,-2.48352680642938e-08,1,0,0,1,3.34561936801547e-23,-1.24176340321469e-08,1,0,0,1,0,-2.48352680642938e-08,1,3.34561936801547e-23,-1.24176340321469e-08,-0.50000011920929,0,-0.866025328636169,-0.499990731477737,-3.60508707331064e-08,-0.866030812263489,-0.499995410442352,-1.80254353665532e-08,-0.866028070449829,-0.499990731477737,-3.60508707331064e-08,-0.866030812263489,-0.50000011920929,0,-0.866025328636169,-0.499995410442352,-1.80254353665532e-08,-0.866028070449829,1,0,-7.45058059692383e-08,1,0,1.47051260057651e-08,1,2.89233536065844e-21,-2.99003417580934e-08,1,0,1.47051260057651e-08,1,0,-7.45058059692383e-08,1,2.89233536065844e-21,-2.99003417580934e-08,-0.499999910593033,0,0.866025507450104,-0.499993622303009,-6.59116858314768e-13,0.866029143333435,-0.49999675154686,-3.2915919881242e-13,0.866027295589447,-0.499993622303009,-6.59116858314768e-13,0.866029143333435,-0.499999910593033,0,0.866025507450104,-0.49999675154686,-3.2915919881242e-13,0.866027295589447,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.499999910593033,-3.02511433871583e-13,-0.866025507450104,0.499993562698364,3.66488789313172e-16,-0.866029143333435,0.499996721744537,-1.5107244611106e-13,-0.866027295589447,0.499993562698364,3.66488789313172e-16,-0.866029143333435,0.499999910593033,-3.02511433871583e-13,-0.866025507450104,0.499996721744537,-1.5107244611106e-13,-0.866027295589447,0.499999910593033,-6.6396429869938e-16,0.866025507450104,0.499993592500687,0,0.866029143333435,0.499996721744537,1.50922175689953e-13,0.866027295589447,0.499993592500687,0,0.866029143333435,0.499999910593033,-6.6396429869938e-16,0.866025507450104,0.499996721744537,1.50922175689953e-13,0.866027295589447
			} 
			TangentsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *336 {
				a: 0.300000071525574,0.0206293053925037,-0.300000011920929,0.0206293053925037,0.300006955862045,0.151095375418663,-0.300000011920929,0.220629319548607,0.18335448205471,-0.364558130502701,-0.18331216275692,-0.364558130502701,1.16626432955513e-08,0.179064810276031,0.300000011920929,0.0206470675766468,-0.300000041723251,0.0206470675766468,0.299999177455902,0.151113137602806,-0.299999296665192,0.151113137602806,0.183338165283203,-0.364546477794647,-0.183338165283203,-0.364546477794647,-2.48352693965614e-09,0.179067716002464,0.300000011920929,0.0206169188022614,-0.300000011920929,0.0206169188022614,0.300000011920929,0.220616936683655,-0.300006985664368,0.151082992553711,0.18331216275692,-0.364558130502701,-0.18335448205471,-0.364558130502701,-1.41463400993302e-08,0.179064810276031,0.299999982118607,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.299999982118607,0.220616936683655,-0.300000041723251,0.220616936683655,0.18335448205471,-0.364558130502701,-0.18331216275692,-0.364558130502701,-1.16626432955513e-08,0.179064810276031,0.299999952316284,0.0206169188022614,-0.299999952316284,0.0206169188022614,0.299999952316284,0.220616936683655,-0.299999952316284,0.220616936683655,0.183338165283203,-0.364546477794647,-0.183338165283203,-0.364546477794647,2.48352693965614e-09,0.179067701101303,0.300000041723251,0.0206169188022614,-0.300000041723251,0.0206169188022614,0.300000041723251,0.220616936683655,-0.300000041723251,0.220616936683655,0.18331216275692,-0.364558130502701,-0.18335448205471,-0.364558130502701,1.41463400993302e-08,0.179064810276031,-0.495029300451279,-0.160688817501068,-0.254573255777359,0.453967213630676,-0.142804279923439,-0.160688817501068,0.00414943555369973,0.214934051036835,0.299999237060547,0.49499323964119,0.600000023841858,-0.0292612910270691,-2.48352693965614e-09,-0.0292613040655851,-0.329998016357422,0.402497678995132,0.329998016357422,0.402497678995132,-0.201671600341797,0.0744631960988045,0.201671600341797,0.0744631960988045,-0.299999237060547,0.49499323964119,0.299999237060547,
0.49499323964119,-2.48352693965614e-09,-0.0292613040655851,0.254578739404678,0.453973531723022,0.495040684938431,-0.16068023443222,-0.00414164690300822,0.2149378657341,0.142818436026573,-0.160684570670128,-0.600000023841858,-0.0292613469064236,-0.299999237060547,0.49499323964119,-2.83957884983366e-10,-0.02926130220294,0.495026081800461,0.160691261291504,0.254571676254272,-0.453965455293655,0.142803832888603,0.160691261291504,-0.00415164092555642,-0.214932978153229,-0.214284658432007,-0.567937433719635,-0.531163692474365,-0.0451458878815174,0.0801578834652901,-0.0451458878815174,-0.329998016357422,0.402497678995132,0.329998016357422,0.402497678995132,-0.201671600341797,0.0744631960988045,0.201671600341797,0.0744631960988045,0.299999237060547,-0.519622802734375,-0.299999237060547,-0.519622802734375,0,0,-0.254578739404678,-0.453973531723022,-0.495040684938431,0.16068023443222,0.00414164690300822,-0.2149378657341,-0.142815664410591,0.160683616995811,0.531163692474365,-0.0451458878815174,0.214284658432007,-0.567937433719635,-0.0801578834652901,-0.0451458878815174,0.299999982118607,0.519615113735199,-0.300000071525574,0.519615054130554,0.329998016357422,0.571594059467316,-0.329998016357422,0.571594059467316,-0.183338165283203,0.317535400390625,-0.366683959960938,0,-0.201671600341797,0.34930419921875,-0.403350830078125,0,0.183338165283203,0.317535400390625,-0.183338165283203,0.317535400390625,0.201671600341797,0.34930419921875,-0.201671600341797,0.34930419921875,-0.299999952316284,0.519615173339844,-0.599999904632568,-1.36469665790173e-07,-0.329997897148132,0.571594178676605,-0.65999972820282,-7.68650139093552e-08,0.366683959960938,0,0.183338165283203,0.317535400390625,0.403354644775391,0,0.201671600341797,0.34930419921875,-0.599999904632568,1.72709739842958e-08,-0.299999833106995,-0.519615232944489,-0.65999972820282,7.68756152069727e-08,-0.329997897148132,-0.571594178676605,0.183338165283203,-0.317535400390625,0.366683959960938,0,0.201671600341797,-0.34930419921875,0.403354644775391,0,-0.299999952316284,-0.519615113735199,
0.299999952316284,-0.519615113735199,-0.329998016357422,-0.571594059467316,0.329998016357422,-0.571594059467316,-0.183338165283203,-0.317535400390625,0.183338165283203,-0.317535400390625,-0.201671600341797,-0.34930419921875,0.201671600341797,-0.34930419921875,0.299999833106995,-0.519615232944489,0.599999904632568,7.68649215387995e-08,0.329997897148132,-0.571594178676605,0.65999972820282,7.68649215387995e-08,-0.366683959960938,0,-0.183338165283203,-0.317535400390625,-0.403350830078125,0,-0.201671600341797,-0.34930419921875,0.599999904632568,-7.68755512581265e-08,0.299999862909317,0.519615232944489,0.65999972820282,-7.68755512581265e-08,0.329997897148132,0.571594178676605,0.300000041723251,0.220608294010162,-0.300000041723251,0.220608294010162,0.299999266862869,0.301287591457367,-0.300006896257401,0.41840261220932,0.600000023841858,0.220616936683655,1.16630216595581e-08,0.220616936683655,0.60000616312027,0.418411254882812,0,0.418411254882812,2.48352738374535e-09,0.220616936683655,-0.600000023841858,0.220616936683655,0,0.418411254882812,-0.599998474121094,0.301296234130859,0.299999982118607,0.220625177025795,-0.300000041723251,0.220625177025795,0.300006866455078,0.418419480323792,-0.299999296665192,0.301304459571838,0.600000023841858,0.220616936683655,2.48352804987917e-09,0.220616936683655,0.599998474121094,0.301296234130859,0,0.418411254882812,-1.41465479330805e-08,0.220616936683655,-0.600000023841858,0.220616936683655,0,0.418411254882812,-0.60000616312027,0.418411254882812,0.299999952316284,0.220625996589661,-0.299999952316284,0.220625996589661,0.299999237060547,0.651587963104248,-0.299999237060547,0.651587963104248,0.600000023841858,0.220616936683655,1.41465479330805e-08,0.220616936683655,0.60000616312027,0.651578903198242,0,0.651578903198242,-1.16630216595581e-08,0.220616936683655,-0.600000023841858,0.220616936683655,0,0.651578903198242,-0.60000616312027,0.651578903198242
			} 
			UVIndex: *234 {
				a: 4,6,5,11,13,12,18,20,19,25,27,26,32,34,33,39,41,40,46,48,47,53,55,54,60,62,61,67,69,68,74,76,75,81,83,82,0,3,1,3,0,2,7,10,8,10,7,9,14,17,15,17,14,16,21,24,22,24,21,23,28,31,29,31,28,30,35,38,36,38,35,37,42,45,43,45,42,44,49,52,50,52,49,51,56,59,57,59,56,58,63,66,64,66,63,65,70,73,71,73,70,72,77,80,78,80,77,79,84,87,85,87,84,86,88,91,89,91,88,90,92,95,93,95,92,94,96,99,97,99,96,98,100,103,101,103,100,102,104,107,105,107,104,106,108,111,109,111,108,110,112,115,113,115,112,114,116,119,117,119,116,118,120,123,121,123,120,122,124,127,125,127,124,126,128,131,129,131,128,130,132,135,133,135,132,134,136,139,137,139,136,138,140,143,141,143,140,142,144,147,145,147,144,146,148,151,149,151,148,150,152,155,153,155,152,154,156,159,157,159,156,158,160,163,161,163,160,162,164,167,165,167,164,166
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2406100385952, "Model::Armor_Plating_Chestplate_Bluemon_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2405957746192, "Material::Interstellar_Armor_Texture_2", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2407160994480, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
	}
	Texture: 2407160992080, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Armor_Plating_Chestplate_Bluemon_Model, Model::RootNode
	C: "OO",2406100385952,0
	
	;Material::Interstellar_Armor_Texture_2, Model::Armor_Plating_Chestplate_Bluemon_Model
	C: "OO",2405957746192,2406100385952
	
	;Geometry::Scene, Model::Armor_Plating_Chestplate_Bluemon_Model
	C: "OO",2404204117440,2406100385952
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture_2
	C: "OO",2407160992080,2405957746192
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture_2
	C: "OP",2407160992080,2405957746192, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2407160994480,2407160992080
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
