﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class Dummy2 : UdonSharpBehaviour
{
    public bool HasInterstellarArmor;

    public TextMeshPro DamageText,DamageTextDPS;
    public bool CanBeHit;
    public float ImmunityFramesDoneDelay = 0.1f;
    public int DPSDamage;

    public ParticleSystem Particle;
    public AudioSource AudioSource;

    public LayerMask damageLayer;
    public int Damage;

    void Start(){
        ImmunityFramesDone();
        SendCustomEventDelayedSeconds(nameof(DPSReseter), 1f);
    }

    public void ImmunityFramesDone(){CanBeHit = true;}

    public void DPSReseter(){
        DamageTextDPS.text = "DPS: " + DPSDamage.ToString();
        DPSDamage = 0;
        SendCustomEventDelayedSeconds(nameof(DPSReseter), 1f);
    }

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && !HasInterstellarArmor)
        {
            Damage = DamageInput.Damage;
            Particle.Stop();
            Particle.Play();
            AudioSource.Play();
            DamageText.text = "Damage: " + Damage.ToString();
            DPSDamage += Damage;

            CanBeHit = false;
            Damage = 0;
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(CanBeHit && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && HasInterstellarArmor)
        {
            if(!DamageInput.CanPenetrateIntestellarArmor)
            {
                Damage = DamageInput.Damage/3;
                Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
            }
            else
            {
                Damage = DamageInput.Damage*3;
                Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
            }
            Particle.Stop();
            Particle.Play();
            AudioSource.Play();
            DamageText.text = "Damage: " + Damage.ToString();
            DPSDamage += Damage;

            CanBeHit = false;
            Damage = 0;
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !HasInterstellarArmor)
        {
            Damage = DamageInput.Damage;
            Particle.Stop();
            Particle.Play();
            AudioSource.Play();
            DamageText.text = "Damage: " + Damage.ToString();
            DPSDamage += Damage;

            CanBeHit = false;
            Damage = 0;
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(CanBeHit && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && HasInterstellarArmor)
        {
            if(!DamageInput.CanPenetrateIntestellarArmor)
            {
                Damage = DamageInput.Damage/2;
                Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
            }
            else
            {
                Damage = DamageInput.Damage*2;
                Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
            }
            Particle.Stop();
            Particle.Play();
            AudioSource.Play();
            DamageText.text = "Damage: " + Damage.ToString();
            DPSDamage += Damage;

            CanBeHit = false;
            Damage = 0;
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}

