﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class RandomColor : UdonSharpBehaviour
{
    public MeshRenderer targetRenderer;

    private void Start()
    {
        if (targetRenderer == null){targetRenderer = GetComponent<MeshRenderer>();}
        if (targetRenderer != null){targetRenderer.material.color = new Color(Random.value, Random.value, Random.value);}
    }
}
