﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CubixEvent : UdonSharpBehaviour
{
    public GameObject EventObject,DeathObject;

    //Event Finisher
    public Transform EventCube;
    public PortalSystem[] PortalSystems;
    public int EnemiesRemain,Finished;

    void Start(){SendCustomEventDelayedSeconds(nameof(Eventfinisher), 0.1f);}

    public void Eventfinisher()
    {
        EnemiesRemain = 0;
        for (int i = 0; i < PortalSystems.Length; i++)
        {
            EnemiesRemain += PortalSystems[i].EnemySlotLeft;
        }

        if(EnemiesRemain > 0){
            EventCube.Rotate(0f, 10f / EnemiesRemain, 0f);
            SendCustomEventDelayedSeconds(nameof(Eventfinisher), 0.02f);
        }
        else{
            SendCustomEventDelayedSeconds(nameof(Exploding), 0.1f);
            SendCustomEventDelayedSeconds(nameof(Eventfinished), 5f);
        }
    }

    public void Exploding(){
        if(Finished == 0){
            EventCube.Rotate(Random.Range(0, 360f), Random.Range(0, 360f), Random.Range(0, 360f));
            SendCustomEventDelayedSeconds(nameof(Exploding), 0.05f);
        }
    }
    public void Eventfinished(){
        EventObject.SetActive(false);
        DeathObject.SetActive(true);
        Finished = 1;
    }
}