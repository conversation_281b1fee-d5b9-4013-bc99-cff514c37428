﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;
using System;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PlayerWorldStartSystem : UdonSharpBehaviour
{
    //Health Variables
    public int HEALTH = 100,MAXHEALTH = 100, ARMOR = 0, MAXARMOR = 3, ProtectionBoost = 1;
    public bool ImmunityFrames, ImmunityFramesConstant, HealImmunityFrames;
    public bool IsDead;
    public TextMeshProUGUI[] HealthText, ArmorText;
    public Slider[] HealthSlider, ArmorSlider;

    //Effects and Sounds
    public AudioSource AudioSource;
    public AudioClip HurtSound, ArmorHurtSound;
    public GameObject HurtIndicator;
    public MeshRenderer HurtMeshRenderer;
    public GameObject LogObject1, LogObject2, LogObject3;
    public LayerMask damageLayer;

    //SpeedBoost Related
    public float SpeedMultiplierCache;
    public bool IsSpeedBoosted;
    //BurnBoos Related
    public GameObject FireBoostObject;
    public bool IsFireBoosted, IsAlreadyOnFire;
    //Protection Boost Related
    public GameObject ProtectionBoostObject;
    public bool IsProtected, IsAlreadyProtected;

    //DeathScreen
    public GameObject DeathScreen, RespawnButton, DeathMusic, DeathSpeech, DeathParticles;
    public AudioClip DeathSound;

    //Additional
    public bool Heightmode = true; //true is Human, false is Konrusk
    public bool FriendlyFire = true;
    public GameObject[] FriendlyFireKnockbackObjects;

    //PrisonRelated
    public bool PlayerInPrison;
    public Collider PrisonCollider;
    public int PrisonCooldown;

    //Videoplayer
    public GameObject VideoPlayer;
    public bool VideoPlayeDisabled;

    //SpawnMenu
    public GameObject[] SpawnMenu;

    private VRCPlayerApi localPlayer;

    private void Start()
    {
        localPlayer = Networking.LocalPlayer;

        //Health
        HEALTH = MAXHEALTH;
        for (int i = 0; i < HealthText.Length; i++){
            HealthText[i].text = HEALTH.ToString();
            HealthSlider[i].value = HEALTH;
        }
        //Armor
        ARMOR = MAXARMOR;
        for (int i = 0; i < ArmorText.Length; i++){
            ArmorText[i].text = ARMOR.ToString();
            ArmorSlider[i].value = ARMOR;
        }

        // Disable manual puppet scaling (no user scaling)
        localPlayer.SetManualAvatarScalingAllowed(false);
        // Set both minimum and maximum eye height to meters
        localPlayer.SetAvatarEyeHeightMinimumByMeters(1.05f);
        localPlayer.SetAvatarEyeHeightMaximumByMeters(1.45f);

        DeathScreen.SetActive(false);
        HurtIndicator.SetActive(false);

        //FriendlyFire knockback objects
        FriendlyFireKnockbackToggle();

        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
        SendCustomEventDelayedSeconds(nameof(UpdateHeight), 0.1f);
    }

    #region CustomUpdate
    public void CustomUpdate()
    {
        //LogObject1.SetActive(true);
        if(!IsDead && HEALTH > 0){
            //Health
            for (int i = 0; i < HealthText.Length; i++){
                HealthText[i].text = HEALTH.ToString();
                HealthSlider[i].value = HEALTH;
            }
            //Armor
            for (int i = 0; i < ArmorText.Length; i++){
                ArmorText[i].text = ARMOR.ToString();
                ArmorSlider[i].value = ARMOR;
            }
        }
        if(!IsDead && HEALTH <= 0){HasDiedFunction();}

        //HurtIndicator
        var color = HurtMeshRenderer.material.color;
        if(color.a > 0){color.a -= 0.05f; HurtMeshRenderer.material.color = color;}
        else{HurtIndicator.SetActive(false);}

        if(PrisonCollider != null && PlayerInPrison){
            if(!PrisonCollider.bounds.Contains(localPlayer.GetPosition())){
                localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);
                if(HEALTH <= 0){
                    IsDead = false;
                    DeathScreen.SetActive(false);
                    HEALTH = MAXHEALTH;
                }
            }
        }

        //CustomTimer
        if(!IsDead){SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);}
    }
    #endregion

    #region SpeedBoost
    //Speed Boost Stuff
    public void SpeedBoostedFunction(float Speed)
    {
        IsSpeedBoosted = true;
        float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
        float SetRunSpeedValue = localPlayer.GetRunSpeed();
        float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
        float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
        float SetGravityStrengthValue = localPlayer.GetGravityStrength();

        localPlayer.SetWalkSpeed(SetWalkSpeedValue*Speed);
        localPlayer.SetRunSpeed(SetRunSpeedValue*Speed);
        localPlayer.SetStrafeSpeed(SetStrafeSpeedValue*Speed);
        localPlayer.SetJumpImpulse(SetJumpImpulseValue*Speed);
        localPlayer.SetGravityStrength(SetGravityStrengthValue*Speed);
        SpeedMultiplierCache = Speed;
        SendCustomEventDelayedSeconds(nameof(SpeedDecreaseFunction), 10f);
    }
    public void SpeedDecreaseFunction()
    {
        IsSpeedBoosted = false;
        float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
        float SetRunSpeedValue = localPlayer.GetRunSpeed();
        float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
        float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
        float SetGravityStrengthValue = localPlayer.GetGravityStrength();

        localPlayer.SetWalkSpeed(SetWalkSpeedValue/SpeedMultiplierCache);
        localPlayer.SetRunSpeed(SetRunSpeedValue/SpeedMultiplierCache);
        localPlayer.SetStrafeSpeed(SetStrafeSpeedValue/SpeedMultiplierCache);
        localPlayer.SetJumpImpulse(SetJumpImpulseValue/SpeedMultiplierCache);
        localPlayer.SetGravityStrength(SetGravityStrengthValue/SpeedMultiplierCache);
        SpeedMultiplierCache = 0;
    }
    #endregion

    #region FireBoost
    public void FireBoostedFunction()
    {
        IsFireBoosted = true;
        FireBoostObject.SetActive(true);
        if(!IsAlreadyOnFire){SendCustomEventDelayedSeconds(nameof(FireDecreaseFunction), 30f);}
        IsAlreadyOnFire = true;
    }
    public void FireDecreaseFunction()
    {
        IsFireBoosted = false;
        IsAlreadyOnFire = false;
        FireBoostObject.SetActive(false);
    }
    #endregion

    #region Protection Boost
    public void ProtectionBoostedFunction()
    {
        IsProtected = true;
        ProtectionBoost = 2;
        ProtectionBoostObject.SetActive(true);
        if(!IsAlreadyProtected){SendCustomEventDelayedSeconds(nameof(ProtectionDecreaseFunction), 15f);}
        IsAlreadyProtected = true;
    }
    public void ProtectionDecreaseFunction()
    {
        localPlayer.SetWalkSpeed(4f);
        localPlayer.SetRunSpeed(7f);
        localPlayer.SetStrafeSpeed(8f);
        localPlayer.SetJumpImpulse(5f);
        localPlayer.SetGravityStrength(1f);

        IsProtected = false;
        IsAlreadyProtected = false;
        ProtectionBoostObject.SetActive(false);
        ProtectionBoost = 1;
    }
    #endregion

    #region Health

    public void HurtIndicatorFunction(int DamageColor)
    {
        if(HEALTH > MAXHEALTH){HEALTH = MAXHEALTH;}
        HurtIndicator.SetActive(true);
        var color = HurtMeshRenderer.material.color;
        color = new Color(0, 0, 0, 0.25f);
        if(DamageColor == 0){color.r = 1.0f;} // Red
        else if(DamageColor == 1){color.g = 0.5f;} // Green
        else if(DamageColor == 2){color.b = 0.5f;} // Blue
        else if(DamageColor == 3){color.g = 1.0f;} // Healing
        else if(DamageColor == 4){color.b = 1.0f;} // MovementIncrease
        HurtMeshRenderer.material.color = color;
    }
    public void HasDiedFunction()
    {
        for (int i = 0; i < HealthText.Length; i++){
            HealthText[i].text = "0";
            HealthSlider[i].value = 0;
        }
        var color = HurtMeshRenderer.material.color;
        color.a = 0;
        HurtMeshRenderer.material.color = color;
        IsDead = true;
        AudioSource.PlayOneShot(DeathSound);
        DeathScreen.SetActive(true);
        DeathMusic.SetActive(true);
        DeathSpeech.SetActive(false);
        DeathParticles.SetActive(false);
        RespawnButton.SetActive(true);
        if(VideoPlayer != null && VideoPlayer.activeSelf && !VideoPlayeDisabled){VideoPlayer.SetActive(false);}
        for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(false);}

        if (localPlayer != null){localPlayer.TeleportTo(DeathScreen.transform.position, DeathScreen.transform.rotation);}
        SendCustomEventDelayedSeconds(nameof(CheckIfRespawned), 30f);
    }
    public void CheckIfRespawned()
    {
        if(IsDead == true){
            DeathSpeech.SetActive(true);
            DeathParticles.SetActive(true);
            RespawnButton.SetActive(false);
            DeathMusic.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(HasRespawnedFunction), 5.1f);
        }
    }
    public void HasRespawnedFunction()
    {
        if(IsDead == true){
            if(VideoPlayer != null && !VideoPlayer.activeSelf && !VideoPlayeDisabled){VideoPlayer.SetActive(true);}
            for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}
            IsDead = false;
            DeathScreen.SetActive(false);
            HEALTH = MAXHEALTH;
            ARMOR = MAXARMOR;
            if (localPlayer != null){localPlayer.TeleportTo(new Vector3(0,0,0), new Quaternion(0,0,0,0));}
            SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
        }
    }

    public void DeactivateImmunityFrames(){ImmunityFrames = false;  ImmunityFramesConstant = false;}
    public void DeactivateHealImmunityFrames(){HealImmunityFrames = false;}

    #endregion

    #region Extras

    //Canonical Height
    public void ToggleHeight(){Heightmode = !Heightmode;}

    public void UpdateHeight()
    {
        //SetAvatarHeight
        if(Heightmode == true && localPlayer.GetAvatarEyeHeightAsMeters() < 1.45f){localPlayer.SetAvatarEyeHeightByMeters(1.45f);}
        else if(Heightmode == false && localPlayer.GetAvatarEyeHeightAsMeters() > 1.05f){localPlayer.SetAvatarEyeHeightByMeters(1.05f);}
        SendCustomEventDelayedSeconds(nameof(UpdateHeight), 5f);
    }

    public void FriendlyFireToggle(){
        FriendlyFire = !FriendlyFire;
        FriendlyFireKnockbackToggle();
    }
    public void FriendlyFireKnockbackToggle(){
        if(FriendlyFire == true){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(true);
                }
            }
        }
        else if(FriendlyFire == false){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(false);
                }
            }
        }
    }


    public void DeactivatePrisonCooldown(){PrisonCooldown = 0;}
    public void VideoPlayerToggle(){VideoPlayeDisabled = !VideoPlayeDisabled;}

    public void DeleteEverything(){Destroy(gameObject);}
    #endregion

    #region Damage
    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFrames){
                if(ARMOR > 0){
                    ARMOR -= 1;

                    if(HEALTH < MAXHEALTH/3){HEALTH -= 0/ProtectionBoost; AudioSource.PlayOneShot(ArmorHurtSound);}
                    else if(HEALTH < MAXHEALTH/2 && HEALTH >= MAXHEALTH/3){HEALTH -= DamageInput.Damage/4/ProtectionBoost; AudioSource.PlayOneShot(ArmorHurtSound);}
                    else if(HEALTH <= MAXHEALTH && HEALTH >= MAXHEALTH/2){HEALTH -= DamageInput.Damage/2/ProtectionBoost; AudioSource.PlayOneShot(ArmorHurtSound);}
                }
                else{HEALTH -= DamageInput.Damage/ProtectionBoost; AudioSource.PlayOneShot(HurtSound);}
                ImmunityFrames = true;
                HurtIndicatorFunction(DamageInput.DamageColor);
                SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);
            }
            if(DamageInput.IsHealing && !HealImmunityFrames){
                HEALTH += DamageInput.Damage;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.2f);
            }
        }
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = !PlayerInPrison;
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
        else if(IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = true;
            HasRespawnedFunction();
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFramesConstant){
                HEALTH -= DamageInput.Damage/ProtectionBoost;
                ImmunityFramesConstant = true;
                AudioSource.PlayOneShot(HurtSound);
                HurtIndicatorFunction(DamageInput.DamageColor);
                SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);
            }
            if(DamageInput.IsHealing && !HealImmunityFrames){
                HEALTH += DamageInput.Damage;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.2f);
            }
        }

        PlayerSpeedInput SpeedInput = collision.gameObject.GetComponent<PlayerSpeedInput>();
        if(!IsDead && SpeedInput != null && SpeedInput.SpeedMultiplier != 0 && IsSpeedBoosted == false){
            SpeedBoostedFunction(SpeedInput.SpeedMultiplier);
            HurtIndicatorFunction(4);
        }
    }
    #endregion
}


