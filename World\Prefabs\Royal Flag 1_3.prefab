%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &294432194934951431
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1154051554320648303}
  - component: {fileID: 514391889498465063}
  - component: {fileID: 634969951787170504}
  - component: {fileID: 1083118994294252769}
  - component: {fileID: 7977207060354975978}
  m_Layer: 11
  m_Name: Cube (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 126
  m_IsActive: 1
--- !u!4 &1154051554320648303
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 294432194934951431}
  serializedVersion: 2
  m_LocalRotation: {x: -0.38268343, y: 0, z: 0, w: 0.92387956}
  m_LocalPosition: {x: 0, y: 0.3582077, z: 0.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: -45, y: 0, z: 0}
--- !u!114 &514391889498465063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 294432194934951431}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000010000000300000002000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040000000500000006000000050000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08000000090000000a000000090000000b0000000a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e0000000d0000000f0000000e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100000001100000012000000110000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001500000016000000150000001700000016000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000000d00000016000000
  - m_Vertices: 010000000400000017000000
  - m_Vertices: 020000000f00000010000000
  - m_Vertices: 030000000600000011000000
  - m_Vertices: 050000000800000015000000
  - m_Vertices: 070000000a00000013000000
  - m_Vertices: 090000000c00000014000000
  - m_Vertices: 0b0000000e00000012000000
  m_SharedTextures: []
  m_Positions:
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  m_Textures0:
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: -0.099999994, y: 0.099999994}
  - {x: 0.100000024, y: 0.099999994}
  - {x: -0.099999994, y: -0.100000024}
  - {x: 0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: -0.100000024}
  - {x: -0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: 0.099999994}
  - {x: -0.100000024, y: 0.099999994}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 1238
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &634969951787170504
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 294432194934951431}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 856306381371736075
  m_Size: {x: 0.2, y: 0.6, z: 0.2}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1235
  m_ShapeBox:
    m_Center: {x: 0.5, y: 2.430399, z: -0.5}
    m_Extent: {x: 0.10000001, y: 0.29999995, z: 0.10000001}
  references:
    version: 2
    RefIds:
    - rid: 856306381371736075
      type: {class: Cube, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data: 
--- !u!23 &1083118994294252769
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 294432194934951431}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: fe3c0770914072944a707c5259a0a8af, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &7977207060354975978
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 294432194934951431}
  m_Mesh: {fileID: 0}
--- !u!1 &1665129612794411969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2555991540062786654}
  - component: {fileID: 4097597642251036081}
  - component: {fileID: 2081112200535938339}
  m_Layer: 22
  m_Name: Cone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2555991540062786654
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665129612794411969}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014386617, y: -0.06855164, z: -0.20488662, w: 0.9762763}
  m_LocalPosition: {x: 0.798, y: 5.463, z: 0.03}
  m_LocalScale: {x: 0.43000004, y: 0.43000004, z: 0.43000004}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 6623170216772511851}
  - {fileID: 3632748236771962501}
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: 0, y: -8.033, z: -23.705}
--- !u!23 &4097597642251036081
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665129612794411969}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c6a8356bfbdbc904493430e7e1248001, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2081112200535938339
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665129612794411969}
  m_Mesh: {fileID: 3613567641014311022, guid: 5b642890f6156c94fa2b3380203a27df, type: 3}
--- !u!1 &2549803141539812563
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7567282961306813200}
  - component: {fileID: 2384823932651159815}
  - component: {fileID: 8635000303418979695}
  - component: {fileID: 9013175658490843357}
  - component: {fileID: 3818066865269224104}
  m_Layer: 11
  m_Name: Cube (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 126
  m_IsActive: 1
--- !u!4 &7567282961306813200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2549803141539812563}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 3.0659428, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2384823932651159815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2549803141539812563}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000010000000300000002000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040000000500000006000000050000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08000000090000000a000000090000000b0000000a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e0000000d0000000f0000000e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100000001100000012000000110000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001500000016000000150000001700000016000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 18000000190000001a000000190000001b0000001a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1c0000001d0000001e0000001d0000001f0000001e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 200000002100000022000000210000002300000022000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 240000002500000026000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 270000002800000029000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2a0000002b0000002c000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2d0000002e0000002f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 300000003100000032000000310000003300000032000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 340000003500000036000000350000003700000036000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 38000000390000003a000000390000003b0000003a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3c0000003d0000003e0000003d0000003f0000003e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 400000004100000042000000410000004300000042000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 440000004500000046000000450000004700000046000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 48000000490000004a000000490000004b0000004a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4c0000004d0000004e0000004d0000004f0000004e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000000d00000012000000
  - m_Vertices: 010000000400000013000000
  - m_Vertices: 020000000f000000140000003100000038000000
  - m_Vertices: 19000000330000003a000000
  - m_Vertices: 0300000006000000150000004000000045000000
  - m_Vertices: 1c0000004200000047000000
  - m_Vertices: 050000000800000011000000
  - m_Vertices: 070000000a000000200000004100000048000000
  - m_Vertices: 1d000000430000004a000000
  - m_Vertices: 090000000c00000010000000
  - m_Vertices: 0b0000000e000000210000003000000035000000
  - m_Vertices: 180000003200000037000000
  - m_Vertices: 160000002400000028000000390000003c000000
  - m_Vertices: 1b0000003b0000003e000000
  - m_Vertices: 17000000250000002a000000440000004d000000
  - m_Vertices: 1e000000460000004f000000
  - m_Vertices: 1a000000360000003f000000
  - m_Vertices: 23000000270000002e000000340000003d000000
  - m_Vertices: 1f0000004b0000004e000000
  - m_Vertices: 220000002b0000002d000000490000004c000000
  - m_Vertices: 26000000290000002c0000002f000000
  m_SharedTextures: []
  m_Positions:
  - {x: -0.099999994, y: -2.4949424, z: 0.099999994}
  - {x: 0.100000024, y: -2.4949424, z: 0.099999994}
  - {x: -0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.100000024, y: -2.4949424, z: 0.099999994}
  - {x: 0.100000024, y: -2.4949424, z: -0.100000024}
  - {x: 0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: 0.100000024, y: -2.4949424, z: -0.100000024}
  - {x: -0.099999994, y: -2.4949424, z: -0.100000024}
  - {x: 0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.099999994, y: -2.4949424, z: -0.100000024}
  - {x: -0.099999994, y: -2.4949424, z: 0.099999994}
  - {x: -0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: -0.099999994, y: -2.4949424, z: -0.100000024}
  - {x: 0.100000024, y: -2.4949424, z: -0.100000024}
  - {x: -0.099999994, y: -2.4949424, z: 0.099999994}
  - {x: 0.100000024, y: -2.4949424, z: 0.099999994}
  - {x: -0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: -0.8199757, y: 2.051601, z: -0.1000124}
  - {x: -0.8199749, y: 2.051601, z: 0.09999981}
  - {x: -0.8199757, y: 2.270084, z: -0.1000124}
  - {x: -0.8199749, y: 2.270084, z: 0.09999981}
  - {x: 0.8199749, y: 2.051601, z: 0.100007236}
  - {x: 0.8199741, y: 2.051601, z: -0.10000496}
  - {x: 0.8199749, y: 2.270084, z: 0.100007236}
  - {x: 0.8199741, y: 2.270084, z: -0.10000496}
  - {x: 0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0, y: 2.4949436, z: 0}
  - {x: -0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0, y: 2.4949436, z: 0}
  - {x: 0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: 0, y: 2.4949436, z: 0}
  - {x: 0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: 0, y: 2.4949436, z: 0}
  - {x: -0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: -0.8199757, y: 2.051601, z: -0.1000124}
  - {x: -0.8199749, y: 2.051601, z: 0.09999981}
  - {x: -0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: -0.8199757, y: 2.270084, z: -0.1000124}
  - {x: -0.8199757, y: 2.051601, z: -0.1000124}
  - {x: -0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: -0.8199749, y: 2.051601, z: 0.09999981}
  - {x: -0.8199749, y: 2.270084, z: 0.09999981}
  - {x: -0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: -0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: -0.8199749, y: 2.270084, z: 0.09999981}
  - {x: -0.8199757, y: 2.270084, z: -0.1000124}
  - {x: 0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: 0.8199749, y: 2.051601, z: 0.100007236}
  - {x: 0.8199741, y: 2.051601, z: -0.10000496}
  - {x: 0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0.099999905, y: 2.0516014, z: 0.1000061}
  - {x: 0.8199749, y: 2.270084, z: 0.100007236}
  - {x: 0.8199749, y: 2.051601, z: 0.100007236}
  - {x: 0.099999905, y: 2.0516014, z: -0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: 0.8199741, y: 2.051601, z: -0.10000496}
  - {x: 0.8199741, y: 2.270084, z: -0.10000496}
  - {x: 0.099999905, y: 2.2700844, z: -0.1000061}
  - {x: 0.099999905, y: 2.2700844, z: 0.1000061}
  - {x: 0.8199741, y: 2.270084, z: -0.10000496}
  - {x: 0.8199749, y: 2.270084, z: 0.100007236}
  m_Textures0:
  - {x: 0.099999994, y: -2.4949422}
  - {x: -0.100000024, y: -2.4949422}
  - {x: 0.099999905, y: 2.0516016}
  - {x: -0.099999905, y: 2.0516016}
  - {x: 0.099999994, y: -2.4949424}
  - {x: -0.100000024, y: -2.4949424}
  - {x: 0.1000061, y: 2.0516014}
  - {x: -0.1000061, y: 2.0516014}
  - {x: 0.100000024, y: -2.4949422}
  - {x: -0.099999994, y: -2.4949422}
  - {x: 0.099999905, y: 2.0516016}
  - {x: -0.099999905, y: 2.0516016}
  - {x: 0.100000024, y: -2.4949424}
  - {x: -0.099999994, y: -2.4949424}
  - {x: 0.1000061, y: 2.0516014}
  - {x: -0.1000061, y: 2.0516014}
  - {x: 0.099999994, y: -0.100000024}
  - {x: -0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: 0.099999994}
  - {x: -0.100000024, y: 0.099999994}
  - {x: 0.099999905, y: 2.0516014}
  - {x: -0.099999905, y: 2.0516014}
  - {x: 0.099999905, y: 2.2700844}
  - {x: -0.099999905, y: 2.2700844}
  - {x: 0.10001557, y: 2.051601}
  - {x: -0.099996634, y: 2.051601}
  - {x: 0.10001557, y: 2.270084}
  - {x: -0.099996634, y: 2.270084}
  - {x: 0.10001041, y: 2.051601}
  - {x: -0.10000179, y: 2.051601}
  - {x: 0.10001041, y: 2.270084}
  - {x: -0.10000179, y: 2.270084}
  - {x: 0.099999905, y: 2.0516014}
  - {x: -0.099999905, y: 2.0516014}
  - {x: 0.099999905, y: 2.2700844}
  - {x: -0.099999905, y: 2.2700844}
  - {x: 0.099999905, y: 2.0335546}
  - {x: -0.099999905, y: 2.0335546}
  - {x: 0, y: 2.27965}
  - {x: 0.1000061, y: 2.0335805}
  - {x: -0.1000061, y: 2.0335805}
  - {x: 0, y: 2.2796733}
  - {x: 0.1000061, y: 2.0335805}
  - {x: -0.1000061, y: 2.0335805}
  - {x: 0, y: 2.2796733}
  - {x: 0.099999905, y: 2.0335546}
  - {x: -0.099999905, y: 2.0335546}
  - {x: 0, y: 2.27965}
  - {x: 0.09999855, y: -0.1000061}
  - {x: 0.09999855, y: 0.1000061}
  - {x: 0.8199743, y: -0.1000124}
  - {x: 0.8199735, y: 0.09999981}
  - {x: -0.10000078, y: 2.2700844}
  - {x: -0.10000078, y: 2.0516014}
  - {x: -0.81997657, y: 2.270084}
  - {x: -0.81997657, y: 2.051601}
  - {x: 0.09999903, y: 2.0516014}
  - {x: 0.09999903, y: 2.2700844}
  - {x: 0.819974, y: 2.051601}
  - {x: 0.819974, y: 2.270084}
  - {x: -0.0999984, y: 0.1000061}
  - {x: -0.0999984, y: -0.1000061}
  - {x: -0.8199734, y: 0.09999981}
  - {x: -0.8199742, y: -0.1000124}
  - {x: -0.09999855, y: 0.1000061}
  - {x: -0.09999855, y: -0.1000061}
  - {x: -0.8199735, y: 0.100007236}
  - {x: -0.81997275, y: -0.10000496}
  - {x: -0.10000006, y: 2.2700844}
  - {x: -0.10000006, y: 2.0516014}
  - {x: -0.8199751, y: 2.270084}
  - {x: -0.8199751, y: 2.051601}
  - {x: 0.09999975, y: 2.0516014}
  - {x: 0.09999975, y: 2.2700844}
  - {x: 0.81997395, y: 2.051601}
  - {x: 0.81997395, y: 2.270084}
  - {x: 0.0999984, y: -0.1000061}
  - {x: 0.0999984, y: 0.1000061}
  - {x: 0.81997263, y: -0.10000496}
  - {x: 0.8199734, y: 0.100007236}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -0.0000038740654, y: 0, z: -1, w: -1}
  - {x: -0.0000038740654, y: 0, z: -1, w: -1}
  - {x: -0.0000038740654, y: 0, z: -1, w: -1}
  - {x: -0.0000038740654, y: 0, z: -1, w: -1}
  - {x: 0.0000038740654, y: 0, z: 1, w: -1}
  - {x: 0.0000038740654, y: 0, z: 1, w: -1}
  - {x: 0.0000038740654, y: 0, z: 1, w: -1}
  - {x: 0.0000038740654, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: -0.00000066229615, z: 0, w: -1}
  - {x: -1, y: -0.00000066229654, z: -7.292389e-26, w: -1}
  - {x: -1, y: -0.00000066229654, z: -7.292389e-26, w: -1}
  - {x: -1, y: -0.0000006622969, z: -1.4584778e-25, w: -1}
  - {x: 1, y: 0, z: 0.000008744379, w: -1}
  - {x: 1, y: 0, z: 0.000008744379, w: -1}
  - {x: 1, y: 0, z: 0.000008744379, w: -1}
  - {x: 1, y: 0, z: 0.000008744379, w: -1}
  - {x: -1, y: 0, z: -0.000008744388, w: -1}
  - {x: -1, y: 0, z: -0.000008744388, w: -1}
  - {x: -1, y: 0, z: -0.000008744388, w: -1}
  - {x: -1, y: 0, z: -0.000008744388, w: -1}
  - {x: 1, y: 0.0000006622968, z: 0, w: -1}
  - {x: 1, y: 0.0000006622965, z: 7.292381e-26, w: -1}
  - {x: 1, y: 0.0000006622965, z: 7.292381e-26, w: -1}
  - {x: 1, y: 0.00000066229615, z: 1.4584762e-25, w: -1}
  - {x: -1, y: 0.0000006622968, z: 0, w: -1}
  - {x: -1, y: 0.0000006622972, z: 0, w: -1}
  - {x: -1, y: 0.0000006622972, z: 0, w: -1}
  - {x: -1, y: 0.00000066229757, z: 0, w: -1}
  - {x: -1, y: 0, z: -0.000001572955, w: -1}
  - {x: -1, y: 0, z: -0.000001572955, w: -1}
  - {x: -1, y: 0, z: -0.000001572955, w: -1}
  - {x: -1, y: 0, z: -0.000001572955, w: -1}
  - {x: 1, y: 0, z: 0.000001583305, w: -1}
  - {x: 1, y: 0, z: 0.000001583305, w: -1}
  - {x: 1, y: 0, z: 0.000001583305, w: -1}
  - {x: 1, y: 0, z: 0.000001583305, w: -1}
  - {x: 1, y: -0.00000066229757, z: 0, w: -1}
  - {x: 1, y: -0.0000006622972, z: 7.2923886e-26, w: -1}
  - {x: 1, y: -0.0000006622972, z: 7.2923886e-26, w: -1}
  - {x: 1, y: -0.0000006622968, z: -1.4584777e-25, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 2146
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &8635000303418979695
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2549803141539812563}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 856306381371736075
  m_Size: {x: 0.2, y: 0.6, z: 0.2}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1235
  m_ShapeBox:
    m_Center: {x: 0.5, y: 2.430399, z: -0.5}
    m_Extent: {x: 0.10000001, y: 0.29999995, z: 0.10000001}
  references:
    version: 2
    RefIds:
    - rid: 856306381371736075
      type: {class: Cube, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data: 
--- !u!23 &9013175658490843357
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2549803141539812563}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: fe3c0770914072944a707c5259a0a8af, type: 2}
  - {fileID: 2100000, guid: e3f28cd601121f049a98f6da8740ad80, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &3818066865269224104
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2549803141539812563}
  m_Mesh: {fileID: 0}
--- !u!1 &3086622873369078119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3632748236771962501}
  - component: {fileID: 3959006121359436287}
  m_Layer: 22
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3632748236771962501
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3086622873369078119}
  m_LocalRotation: {x: -0.19049206, y: -0, z: -0, w: 0.9816888}
  m_LocalPosition: {x: 0, y: 0, z: 0.27200007}
  m_LocalScale: {x: 0.049999997, y: 0.049999997, z: 0.049999997}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2555991540062786654}
  m_LocalEulerAnglesHint: {x: -21.963, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -0.041}
  m_SizeDelta: {x: 0.1, y: 0.1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!212 &3959006121359436287
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3086622873369078119}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1275f31b22d32c742823aedbfb5a84f1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 0220ca8ac0b462c4b863204236c363bf, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.32, y: 0.32}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &3867573040458960667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4796606168970728980}
  - component: {fileID: 8827555939915466951}
  - component: {fileID: 2659363262476158066}
  - component: {fileID: 7517109921248236888}
  - component: {fileID: 8847897829134608653}
  m_Layer: 11
  m_Name: Royal Flag 1_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &4796606168970728980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3867573040458960667}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.8368592, y: 18.672192, z: 154.13301}
  m_LocalScale: {x: 24, y: 24, z: 24}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 8447716457153254206}
  - {fileID: 4062225393355923993}
  - {fileID: 1154051554320648303}
  - {fileID: 7567282961306813200}
  - {fileID: 5140550185958493383}
  - {fileID: 2555991540062786654}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8827555939915466951
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3867573040458960667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000010000000300000002000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040000000500000006000000050000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08000000090000000a000000090000000b0000000a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e0000000d0000000f0000000e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100000001100000012000000110000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001500000016000000150000001700000016000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000000d00000016000000
  - m_Vertices: 010000000400000017000000
  - m_Vertices: 020000000f00000010000000
  - m_Vertices: 030000000600000011000000
  - m_Vertices: 050000000800000015000000
  - m_Vertices: 070000000a00000013000000
  - m_Vertices: 090000000c00000014000000
  - m_Vertices: 0b0000000e00000012000000
  m_SharedTextures: []
  m_Positions:
  - {x: -0.6022427, y: -0.090335846, z: 0.60224915}
  - {x: 0.6022427, y: -0.090335846, z: 0.60224915}
  - {x: -0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: 0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: 0.6022427, y: -0.090335846, z: 0.60224915}
  - {x: 0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: 0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: 0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: 0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: -0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: 0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: -0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: -0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: -0.6022427, y: -0.090335846, z: 0.60224915}
  - {x: -0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: -0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: -0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: 0.4212556, y: 0.19736099, z: 0.42126465}
  - {x: -0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: 0.4212556, y: 0.19736099, z: -0.42126465}
  - {x: -0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: 0.6022427, y: -0.090335846, z: -0.60224915}
  - {x: -0.6022427, y: -0.090335846, z: 0.60224915}
  - {x: 0.6022427, y: -0.090335846, z: 0.60224915}
  m_Textures0:
  - {x: 0.6022427, y: -0.39714995}
  - {x: -0.6022427, y: -0.39714995}
  - {x: 0.4212556, y: -0.057260487}
  - {x: -0.4212556, y: -0.057260487}
  - {x: 0.60224915, y: -0.39714956}
  - {x: -0.60224915, y: -0.39714956}
  - {x: 0.42126465, y: -0.05725868}
  - {x: -0.42126465, y: -0.05725868}
  - {x: 0.6022427, y: -0.39714995}
  - {x: -0.6022427, y: -0.39714995}
  - {x: 0.4212556, y: -0.057260487}
  - {x: -0.4212556, y: -0.057260487}
  - {x: 0.60224915, y: -0.39714956}
  - {x: -0.60224915, y: -0.39714956}
  - {x: 0.42126465, y: -0.05725868}
  - {x: -0.42126465, y: -0.05725868}
  - {x: -0.4212556, y: 0.42126465}
  - {x: 0.4212556, y: 0.42126465}
  - {x: -0.4212556, y: -0.42126465}
  - {x: 0.4212556, y: -0.42126465}
  - {x: 0.6022427, y: -0.60224915}
  - {x: -0.6022427, y: -0.60224915}
  - {x: 0.6022427, y: 0.60224915}
  - {x: -0.6022427, y: 0.60224915}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 865
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &2659363262476158066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3867573040458960667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 856306381371736074
  m_Size: {x: 2, y: 0.3, z: 2}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 626
  m_ShapeBox:
    m_Center: {x: 2.75, y: 0.5, z: -2.25}
    m_Extent: {x: 1, y: 0.14999999, z: 1}
  references:
    version: 2
    RefIds:
    - rid: 856306381371736074
      type: {class: Cube, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data: 
--- !u!23 &7517109921248236888
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3867573040458960667}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f09402f061ed3aa488df71655a6ea34e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &8847897829134608653
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3867573040458960667}
  m_Mesh: {fileID: 0}
--- !u!1 &4498806766276829930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8447716457153254206}
  - component: {fileID: 6248561481547508481}
  - component: {fileID: 4571991407137981317}
  - component: {fileID: 8542865015704796235}
  - component: {fileID: 7613265706421871432}
  m_Layer: 11
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 126
  m_IsActive: 1
--- !u!4 &8447716457153254206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4498806766276829930}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35355338, y: 0.35355338, z: -0.1464466, w: 0.8535535}
  m_LocalPosition: {x: -0.2, y: 0.3582077, z: -0.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: 45, y: 45, z: 0}
--- !u!114 &6248561481547508481
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4498806766276829930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000010000000300000002000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040000000500000006000000050000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08000000090000000a000000090000000b0000000a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e0000000d0000000f0000000e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100000001100000012000000110000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001500000016000000150000001700000016000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000000d00000016000000
  - m_Vertices: 010000000400000017000000
  - m_Vertices: 020000000f00000010000000
  - m_Vertices: 030000000600000011000000
  - m_Vertices: 050000000800000015000000
  - m_Vertices: 070000000a00000013000000
  - m_Vertices: 090000000c00000014000000
  - m_Vertices: 0b0000000e00000012000000
  m_SharedTextures: []
  m_Positions:
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  m_Textures0:
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: -0.099999994, y: 0.099999994}
  - {x: 0.100000024, y: 0.099999994}
  - {x: -0.099999994, y: -0.100000024}
  - {x: 0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: -0.100000024}
  - {x: -0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: 0.099999994}
  - {x: -0.100000024, y: 0.099999994}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 1238
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &4571991407137981317
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4498806766276829930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 856306381371736075
  m_Size: {x: 0.2, y: 0.6, z: 0.2}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1235
  m_ShapeBox:
    m_Center: {x: 0.5, y: 2.430399, z: -0.5}
    m_Extent: {x: 0.10000001, y: 0.29999995, z: 0.10000001}
  references:
    version: 2
    RefIds:
    - rid: 856306381371736075
      type: {class: Cube, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data: 
--- !u!23 &8542865015704796235
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4498806766276829930}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: fe3c0770914072944a707c5259a0a8af, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &7613265706421871432
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4498806766276829930}
  m_Mesh: {fileID: 0}
--- !u!1 &4587395420441350158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4062225393355923993}
  - component: {fileID: 8234839408353104360}
  - component: {fileID: 1350326849368565433}
  - component: {fileID: 5297686034042841256}
  - component: {fileID: 6230294522777631554}
  m_Layer: 11
  m_Name: Cube (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 126
  m_IsActive: 1
--- !u!4 &4062225393355923993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4587395420441350158}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35355338, y: -0.35355338, z: 0.1464466, w: 0.8535535}
  m_LocalPosition: {x: 0.2, y: 0.3582077, z: -0.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: 45, y: -45, z: 0}
--- !u!114 &8234839408353104360
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4587395420441350158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000010000000300000002000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040000000500000006000000050000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08000000090000000a000000090000000b0000000a000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e0000000d0000000f0000000e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100000001100000012000000110000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001500000016000000150000001700000016000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000000d00000016000000
  - m_Vertices: 010000000400000017000000
  - m_Vertices: 020000000f00000010000000
  - m_Vertices: 030000000600000011000000
  - m_Vertices: 050000000800000015000000
  - m_Vertices: 070000000a00000013000000
  - m_Vertices: 090000000c00000014000000
  - m_Vertices: 0b0000000e00000012000000
  m_SharedTextures: []
  m_Positions:
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: 0.29999995, z: 0.099999994}
  - {x: -0.099999994, y: 0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: 0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: -0.100000024}
  - {x: 0.100000024, y: -0.29999995, z: -0.100000024}
  - {x: -0.099999994, y: -0.29999995, z: 0.099999994}
  - {x: 0.100000024, y: -0.29999995, z: 0.099999994}
  m_Textures0:
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.099999994, y: -0.29999995}
  - {x: -0.100000024, y: -0.29999995}
  - {x: 0.099999994, y: 0.29999995}
  - {x: -0.100000024, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: 0.100000024, y: -0.29999995}
  - {x: -0.099999994, y: -0.29999995}
  - {x: 0.100000024, y: 0.29999995}
  - {x: -0.099999994, y: 0.29999995}
  - {x: -0.099999994, y: 0.099999994}
  - {x: 0.100000024, y: 0.099999994}
  - {x: -0.099999994, y: -0.100000024}
  - {x: 0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: -0.100000024}
  - {x: -0.100000024, y: -0.100000024}
  - {x: 0.099999994, y: 0.099999994}
  - {x: -0.100000024, y: 0.099999994}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 0, y: 0, z: 1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 0, y: 0, z: -1, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 1238
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &1350326849368565433
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4587395420441350158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 856306381371736075
  m_Size: {x: 0.2, y: 0.6, z: 0.2}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1235
  m_ShapeBox:
    m_Center: {x: 0.5, y: 2.430399, z: -0.5}
    m_Extent: {x: 0.10000001, y: 0.29999995, z: 0.10000001}
  references:
    version: 2
    RefIds:
    - rid: 856306381371736075
      type: {class: Cube, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data: 
--- !u!23 &5297686034042841256
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4587395420441350158}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: fe3c0770914072944a707c5259a0a8af, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &6230294522777631554
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4587395420441350158}
  m_Mesh: {fileID: 0}
--- !u!1 &7888839328763738383
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5140550185958493383}
  - component: {fileID: 1755357218103636490}
  m_Layer: 11
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 126
  m_IsActive: 1
--- !u!224 &5140550185958493383
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7888839328763738383}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.13200015}
  m_LocalScale: {x: 7.4000006, y: 7.4000006, z: 7.4000006}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 4796606168970728980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 4.1410003}
  m_SizeDelta: {x: 0.5, y: 0.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!212 &1755357218103636490
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7888839328763738383}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1275f31b22d32c742823aedbfb5a84f1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: c9fd70af7c60611448cfd7df1d38e695, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.32, y: 0.32}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &9087736803407930982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6623170216772511851}
  - component: {fileID: 1638974703406425832}
  m_Layer: 22
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6623170216772511851
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9087736803407930982}
  m_LocalRotation: {x: 0.19049083, y: -0, z: -0, w: 0.98168904}
  m_LocalPosition: {x: 0, y: 0, z: -0.27200007}
  m_LocalScale: {x: 0.049999997, y: 0.05, z: 0.05}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2555991540062786654}
  m_LocalEulerAnglesHint: {x: 21.963, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -0.041}
  m_SizeDelta: {x: 0.1, y: 0.1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!212 &1638974703406425832
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9087736803407930982}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1275f31b22d32c742823aedbfb5a84f1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 0220ca8ac0b462c4b863204236c363bf, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.32, y: 0.32}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
