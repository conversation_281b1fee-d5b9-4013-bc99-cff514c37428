
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRCLinking;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PatreonSupporterCheckSystem : UdonSharpBehaviour
{
    public string roleId;
    public string RoleName;
    public VrcLinkingDownloader linkingDownloader;
    public Collider[] Colliders;
    public GameObject[] GameObjectsOpen, GameObjectsClose;
    public VRC_Pickup[] Pickups;

    void Start()
    {
        VRCPlayerApi localPlayer = Networking.LocalPlayer;
        if (localPlayer == null) return;
        
        string[] members;
        if (linkingDownloader.TryGetGuildMembersByRoleId(roleId, out members))
        {
            foreach (string memberName in members)
            {
                if (localPlayer.displayName == memberName)
                {
                    foreach (GameObject targetObjectInstance in GameObjectsOpen)
                    {
                        if (targetObjectInstance != null){targetObjectInstance.SetActive(true);}
                    }
                    foreach (GameObject targetObjectInstance in GameObjectsClose)
                    {
                        if (targetObjectInstance != null){targetObjectInstance.SetActive(false);}
                    }
                    foreach (Collider ObjectCollider in Colliders)
                    {
                        if (ObjectCollider != null){ObjectCollider.enabled = false;}
                    }
                    foreach (VRC_Pickup VRC_PickupObject in Pickups)
                    {
                        if (VRC_PickupObject != null){VRC_PickupObject.pickupable = true;}
                    }
                    break;
                }
            }
        }
    }
}

