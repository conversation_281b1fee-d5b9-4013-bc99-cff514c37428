using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Economy;
using UnityEngine.UI;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GenericSocialSystem : UdonSharpBehaviour
{
    public string vrchatGroupId = "";
    [Tooltip("Open group store page instead of group info page")]
    public bool openGroupStorePage = false;

    [Head<PERSON>("Discord Settings (SocialType = 2)")]
    [Tooltip("Discord invite link (for display/copying only - cannot open in VRChat)")]
    public string discordInviteLink = "https://discord.gg/your-server";
    [Tooltip("Discord server name for display")]
    public string discordServerName = "Your Discord Server";

    [Header("Patreon Settings (SocialType = 3)")]
    [Tooltip("Patreon page link (for display/copying only - cannot open in VRChat)")]
    public string patreonLink = "https://www.patreon.com/your-page";
    [Toolt<PERSON>("Patreon creator name for display")]
    public string patreonCreatorName = "Your Patreon";

    [Header("UI Elements (Optional)")]
    [Tooltip("Text component to display link information")]
    public Text linkDisplayText;
    [Tooltip("TextMeshPro component to display link information")]
    public TextMeshProUGUI linkDisplayTextTMP;
    [Tooltip("Input field for users to copy links")]
    public InputField linkInputField;
    [Tooltip("TMP Input field for users to copy links")]
    public TMP_InputField linkInputFieldTMP;

    private void UpdateUIDisplay()
    {
        string displayText = "";
        string linkText = "";

        switch (SocialType)
        {
            case 1: // VRChat Group
                displayText = $"Join our VRChat Group!\nGroup ID: {vrchatGroupId}";
                linkText = $"vrchat://launch?ref=vrchat.com&id={vrchatGroupId}";
                break;

            case 2: // Discord
                displayText = $"Join our Discord: {discordServerName}\n⚠️ Link must be copied manually";
                linkText = discordInviteLink;
                break;

            case 3: // Patreon
                displayText = $"Support us on Patreon: {patreonCreatorName}\n⚠️ Link must be copied manually";
                linkText = patreonLink;
                break;
        }

        // Update text displays
        if (linkDisplayText != null)
            linkDisplayText.text = displayText;

        if (linkDisplayTextTMP != null)
            linkDisplayTextTMP.text = displayText;

        // Update input fields with copyable links
        if (linkInputField != null)
        {
            linkInputField.text = linkText;
            linkInputField.readOnly = true;
        }

        if (linkInputFieldTMP != null)
        {
            linkInputFieldTMP.text = linkText;
            linkInputFieldTMP.readOnly = true;
        }
    }

    public void Showgroup()
    {
        if (SocialType != 1)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[GenericSocialSystem] Showgroup() called but SocialType is not set to 1 (VRChat Group)");
            return;
        }

        if (string.IsNullOrEmpty(vrchatGroupId))
        {
            Debug.LogError("[GenericSocialSystem] VRChat Group ID is empty! Please set a valid group ID.");
            return;
        }

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] Opening VRChat Group: {vrchatGroupId} (Store Page: {openGroupStorePage})");

        try
        {
            if (openGroupStorePage)
            {
                // Open the group's store page
                Store.OpenGroupStorePage(vrchatGroupId);
            }
            else
            {
                // Open the group's info page
                Store.OpenGroupPage(vrchatGroupId);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[GenericSocialSystem] Failed to open VRChat group page: {e.Message}");
        }
    }

    public void Showdiscord()
    {
        if (SocialType != 2)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[GenericSocialSystem] Showdiscord() called but SocialType is not set to 2 (Discord)");
            return;
        }

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] Discord link requested: {discordInviteLink}");

        // VRChat cannot open external URLs, so we provide alternative methods
        ShowExternalLinkInfo("Discord", discordServerName, discordInviteLink);
    }

    public void Showpatreon()
    {
        if (SocialType != 3)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[GenericSocialSystem] Showpatreon() called but SocialType is not set to 3 (Patreon)");
            return;
        }

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] Patreon link requested: {patreonLink}");

        // VRChat cannot open external URLs, so we provide alternative methods
        ShowExternalLinkInfo("Patreon", patreonCreatorName, patreonLink);
    }

    private void ShowExternalLinkInfo(string platformName, string serviceName, string link)
    {
        // Since VRChat cannot open external URLs, we provide the link for manual copying
        string message = $"🔗 {platformName} Link Available!\n" +
                        $"Service: {serviceName}\n" +
                        $"⚠️ VRChat cannot open external links automatically.\n" +
                        $"Please copy the link from the input field and paste it in your browser.\n" +
                        $"Link: {link}";

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] {message}");

        // Update UI to show the link information
        UpdateLinkDisplay(platformName, serviceName, link);

        // Try to copy to system clipboard if possible (this may not work in VRChat)
        TryCopyToClipboard(link);
    }

    private void UpdateLinkDisplay(string platformName, string serviceName, string link)
    {
        string displayMessage = $"📋 Copy {platformName} Link:\n{serviceName}\n\n⬇️ Copy from field below ⬇️";

        // Update text displays
        if (linkDisplayText != null)
            linkDisplayText.text = displayMessage;

        if (linkDisplayTextTMP != null)
            linkDisplayTextTMP.text = displayMessage;

        // Update input fields with the link for easy copying
        if (linkInputField != null)
        {
            linkInputField.text = link;
            linkInputField.readOnly = true;
            // Try to select all text for easy copying
            StartCoroutine(SelectInputFieldText());
        }

        if (linkInputFieldTMP != null)
        {
            linkInputFieldTMP.text = link;
            linkInputFieldTMP.readOnly = true;
            // Try to select all text for easy copying
            StartCoroutine(SelectTMPInputFieldText());
        }
    }

    private System.Collections.IEnumerator SelectInputFieldText()
    {
        yield return null; // Wait one frame
        if (linkInputField != null)
        {
            linkInputField.Select();
            linkInputField.selectionAnchorPosition = 0;
            linkInputField.selectionFocusPosition = linkInputField.text.Length;
        }
    }

    private System.Collections.IEnumerator SelectTMPInputFieldText()
    {
        yield return null; // Wait one frame
        if (linkInputFieldTMP != null)
        {
            linkInputFieldTMP.Select();
            linkInputFieldTMP.selectionAnchorPosition = 0;
            linkInputFieldTMP.selectionFocusPosition = linkInputFieldTMP.text.Length;
        }
    }

    private void TryCopyToClipboard(string text)
    {
        // Note: This may not work in VRChat runtime, but we can try
        try
        {
            GUIUtility.systemCopyBuffer = text;
            if (enableDebugLogging)
                Debug.Log($"[GenericSocialSystem] Attempted to copy to clipboard: {text}");
        }
        catch (System.Exception e)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"[GenericSocialSystem] Failed to copy to clipboard: {e.Message}");
        }
    }

    // Public methods for manual testing and external calls
    public void OpenVRChatGroup()
    {
        Showgroup();
    }

    public void ShowDiscordLink()
    {
        Showdiscord();
    }

    public void ShowPatreonLink()
    {
        Showpatreon();
    }

    // Method to change social type at runtime
    public void SetSocialType(int newType)
    {
        if (newType >= 1 && newType <= 3)
        {
            SocialType = newType;
            UpdateUIDisplay();

            if (enableDebugLogging)
                Debug.Log($"[GenericSocialSystem] Social type changed to: {SocialType}");
        }
        else
        {
            Debug.LogError($"[GenericSocialSystem] Invalid social type: {newType}. Must be 1 (Group), 2 (Discord), or 3 (Patreon)");
        }
    }

    // Method to update group ID at runtime
    public void SetVRChatGroupId(string newGroupId)
    {
        vrchatGroupId = newGroupId;
        if (SocialType == 1)
            UpdateUIDisplay();

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] VRChat Group ID updated to: {newGroupId}");
    }

    // Method to update Discord link at runtime
    public void SetDiscordLink(string newLink, string newServerName)
    {
        discordInviteLink = newLink;
        discordServerName = newServerName;
        if (SocialType == 2)
            UpdateUIDisplay();

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] Discord link updated: {newLink}");
    }

    // Method to update Patreon link at runtime
    public void SetPatreonLink(string newLink, string newCreatorName)
    {
        patreonLink = newLink;
        patreonCreatorName = newCreatorName;
        if (SocialType == 3)
            UpdateUIDisplay();

        if (enableDebugLogging)
            Debug.Log($"[GenericSocialSystem] Patreon link updated: {newLink}");
    }

    // Utility method to get current configuration info
    public string GetCurrentConfigInfo()
    {
        switch (SocialType)
        {
            case 1:
                return $"VRChat Group: {vrchatGroupId} (Store: {openGroupStorePage})";
            case 2:
                return $"Discord: {discordServerName} - {discordInviteLink}";
            case 3:
                return $"Patreon: {patreonCreatorName} - {patreonLink}";
            default:
                return "Invalid SocialType";
        }
    }
}
