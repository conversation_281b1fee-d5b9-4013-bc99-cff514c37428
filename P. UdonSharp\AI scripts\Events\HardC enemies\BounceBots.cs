
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BounceBots : UdonSharpBehaviour
{
    private Rigidbody rb;
    public int force = 50;
    public int BounceBotType; //0 is normal, 1 is dash, 2 is nottargetting

    private VRCPlayerApi localPlayer;

    //NewAISystem
    public GameObject[] DashIndications;
    public int State; //0 is green, 1 is yellow, 2 is red, 3 is dash
    public int DashCooldown,DashCooldownMax = 10;
    public bool IsDashing;

    //Effects
    public AudioSource Metal,ParryIndicatorSound;
    public AudioClip Metal2,NormalSound;
    public ParticleSystem ParryIndicator;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        rb = GetComponent<Rigidbody>();
        if(BounceBotType == 1){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);}
    }


    public void UpdateCustom()
    {
        if (localPlayer == null) return;

        if(BounceBotType == 1){
            if(DashCooldown < DashCooldownMax){DashCooldown++;}

            if(DashCooldown <= 0){
                State = -1;
                DashIndications[0].SetActive(false);
                DashIndications[1].SetActive(false);
                DashIndications[2].SetActive(false);
            }
            else if(DashCooldown > 0 && DashCooldown <= DashCooldownMax / 3){
                State = 0;
                DashIndications[0].SetActive(true);
                DashIndications[1].SetActive(false);
                DashIndications[2].SetActive(false);
            }
            else if(DashCooldown > DashCooldownMax / 3 && DashCooldown <= DashCooldownMax / 2){
                State = 1;
                DashIndications[0].SetActive(false);
                DashIndications[1].SetActive(true);
                DashIndications[2].SetActive(false);
            }
            else if(DashCooldown > DashCooldownMax / 2 && DashCooldown < DashCooldownMax){
                State = 2;
                DashIndications[0].SetActive(false);
                DashIndications[1].SetActive(false);
                DashIndications[2].SetActive(true);
            }
            else if(DashCooldown >= DashCooldownMax){
                if(!IsDashing){
                    State = 3;
                    ParryIndicator.Play();
                    rb.velocity = Vector3.zero;
                    rb.drag = 500;
                    //rb.useGravity = false;
                    ParryIndicatorSound.PlayOneShot(ParryIndicatorSound.clip);
                    IsDashing = true;
                    SendCustomEventDelayedSeconds(nameof(Dash), 1f);
                }
            }
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
        }
    }
    public void Dash()
    {
        rb.drag = 0;
        Vector3 directionToPlayer = transform.position - (localPlayer.GetPosition() + new Vector3(0,2f,0));
        rb.AddForce(directionToPlayer.normalized * -25, ForceMode.Impulse);
    }
    public void ResetState(){
        rb.drag = 0;
        IsDashing = false;
        //rb.useGravity = true;
    }


    //when collision happens
    private void OnCollisionEnter(Collision collision)
    {
        if(BounceBotType == 1 || BounceBotType == 0){
            Vector3 PlayerDirection = localPlayer.GetPosition();
            Vector3 inDirection = collision.contacts[0].normal;
            Vector3 outDirection = -inDirection + (transform.position - localPlayer.GetPosition()).normalized*0.3f;
            int layer = collision.gameObject.layer;

            if(BounceBotType == 1){
                if(layer == LayerMask.NameToLayer("Pickup"))
                {
                    if(State == -1){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 0){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 1){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 2){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                    }
                    else if(State == 3)
                    {
                        Metal.PlayOneShot(Metal2);
                        DashCooldown = -5;
                        rb.AddForce(outDirection * -force*3, ForceMode.Impulse);
                    }
                }
                else{
                    if(State == -1){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 0){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 1){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 2){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force, ForceMode.Impulse);
                    }
                    else if(State == 3){
                        Metal.PlayOneShot(NormalSound);
                        rb.AddForce(outDirection * -force/4, ForceMode.Impulse);
                        DashCooldown = 0;
                        ResetState();
                    }
                }
            }
            else if(BounceBotType == 0){
                if(layer == LayerMask.NameToLayer("Pickup"))
                {
                    Metal.PlayOneShot(Metal2);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                }
                else{
                    Metal.PlayOneShot(NormalSound);
                    rb.AddForce(outDirection * -force, ForceMode.Impulse);
                }
            }
        }
        else if(BounceBotType == 2){
            Vector3 inDirection = collision.contacts[0].normal;
            Vector3 outDirection = -inDirection;
        
            Metal.PlayOneShot(NormalSound);
            rb.AddForce(outDirection * -force, ForceMode.Impulse);
        }
    }
    //when collision happens
    private void OnTriggerEnter(Collider collision)
    {

        Vector3 PlayerDirection = localPlayer.GetPosition();

        // Calculate collision normal based on relative positions since we don't have collision contacts
        Vector3 inDirection = (collision.transform.position - transform.position).normalized;
        Vector3 outDirection = -inDirection;
        int layer = collision.gameObject.layer;

        if(BounceBotType == 1){
            if(layer == LayerMask.NameToLayer("Pickup"))
            {
                if(State == -1){
                    Metal.PlayOneShot(NormalSound);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                }
                else if(State == 0){
                    Metal.PlayOneShot(NormalSound);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                }
                else if(State == 1){
                    Metal.PlayOneShot(NormalSound);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                }
                else if(State == 2){
                    Metal.PlayOneShot(NormalSound);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                }
                else if(State == 3){
                    Metal.PlayOneShot(Metal2);
                    rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                    DashCooldown = 0;
                    ResetState();
                }
            }
        }
        else if(BounceBotType == 0){
            if(layer == LayerMask.NameToLayer("Pickup"))
            {
                rb.AddForce(outDirection * -force*2, ForceMode.Impulse);
                Metal.PlayOneShot(Metal2);
            }
        }
    }
}


