; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 20
		Hour: 0
		Minute: 27
		Second: 43
		Millisecond: 465
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\w4y06t20_Bluemon Spear Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\w4y06t20_Bluemon Spear Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2305885957424, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 5
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 2
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2311113378112, "Geometry::Scene", "Mesh" {
		Vertices: *39 {
			a: 2.50015258789062,-116.600799560547,2.4993896484375,-2.50015258789062,-116.600799560547,2.4993896484375,2.49999985098839,102.300071716309,2.49999985098839,-2.5000000372529,102.300071716309,2.49999985098839,-2.50015258789062,-116.600799560547,-2.4993896484375,-2.5000000372529,102.300071716309,-2.5000000372529,2.50015258789062,-116.600799560547,-2.4993896484375,2.49999985098839,102.300071716309,-2.5000000372529,10.9199523925781,102.300071716309,10.9161376953125,-10.9199523925781,102.300071716309,10.9161376953125,10.9199523925781,102.300071716309,-10.9161376953125,-10.9199523925781,102.300071716309,-10.9161376953125,-0,116.600799560547,0
		} 
		PolygonVertexIndex: *56 {
			a: 0,2,3,-2,1,3,5,-5,4,5,7,-7,6,7,2,-1,6,0,1,-5,8,12,-10,10,12,-9,9,12,-12,11,12,-11,2,9,-4,9,2,-9,7,8,-3,8,7,-11,3,11,-6,11,3,-10,5,10,-8,10,5,-12
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *168 {
				a: -0,-2.78757488558767e-06,1,-0,-2.78757488558767e-06,1,-0,-2.78757488558767e-06,1,-0,-2.78757488558767e-06,1,-1,6.96893721396918e-07,0,-1,6.96893721396918e-07,0,-1,6.96893721396918e-07,0,-1,6.96893721396918e-07,0,-0,-2.78842571788118e-06,-1,-0,-2.78842571788118e-06,-1,-0,-2.78842571788118e-06,-1,-0,-2.78842571788118e-06,-1,1,6.97744610533846e-07,0,1,6.97744553690427e-07,0,1,6.97744553690427e-07,0,1,6.97744553690427e-07,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0.60675847530365,0.794886231422424,-0,0.60675847530365,0.794886231422424,-0,0.60675847530365,0.794886231422424,0.794784009456635,0.60689240694046,0,0.794784009456635,0.60689240694046,0,0.794784009456635,0.60689240694046,0,-0.794784009456635,0.60689240694046,0,-0.794784009456635,0.60689240694046,0,-0.794784009456635,0.60689240694046,0,-0,0.60675847530365,-0.794886231422424,-0,0.60675847530365,-0.794886231422424,-0,0.60675847530365,-0.794886231422424,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0
			} 
			NormalsW: *56 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *168 {
				a: -0,1,2.78757488558767e-06,-0,1,2.78757488558767e-06,-0,1,2.78757488558767e-06,-0,1,2.78757488558767e-06,6.96893721396918e-07,1,-0,6.96893721396918e-07,1,-0,6.96893721396918e-07,1,-0,6.96893721396918e-07,1,-0,0,1,-2.78842571788118e-06,0,1,-2.78842571788118e-06,0,1,-2.78842571788118e-06,0,1,-2.78842571788118e-06,-6.97744610533846e-07,1,0,-6.97744553690427e-07,1,0,-6.97744553690427e-07,1,0,-6.97744553690427e-07,1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,0.794886231422424,-0.60675847530365,0,0.794886231422424,-0.60675847530365,0,0.794886231422424,-0.60675847530365,-0.60689240694046,0.794784009456635,0,-0.60689240694046,0.794784009456635,0,-0.60689240694046,0.794784009456635,0,0.60689240694046,0.794784009456635,-0,0.60689240694046,0.794784009456635,-0,0.60689240694046,0.794784009456635,-0,0,0.794886231422424,0.60675847530365,0,0.794886231422424,0.60675847530365,0,0.794886231422424,0.60675847530365,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1
			} 
			BinormalsW: *56 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *168 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *56 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *96 {
				a: 0.0250015258789062,-1.16600787639618,-0.0250015258789062,-1.16600787639618,0.0249999985098839,1.02300083637238,-0.025000000372529,1.02300083637238,0.024993896484375,-1.16600799560547,-0.024993896484375,-1.16600799560547,0.0249999985098839,1.02300071716309,-0.025000000372529,1.02300071716309,0.0250015258789062,-1.16600787639618,-0.0250015258789062,-1.16600787639618,0.025000000372529,1.02300083637238,-0.0249999985098839,1.02300083637238,0.024993896484375,-1.16600799560547,-0.024993896484375,-1.16600799560547,0.025000000372529,1.02300071716309,-0.0249999985098839,1.02300071716309,0.0250015258789062,-0.024993896484375,-0.0250015258789062,-0.024993896484375,0.0250015258789062,0.024993896484375,-0.0250015258789062,0.024993896484375,0.0249999985098839,0.0249999985098839,-0.025000000372529,0.0249999985098839,0.109199523925781,0.109161376953125,-0.109199523925781,0.109161376953125,0.0249999985098839,-0.025000000372529,0.0249999985098839,0.0249999985098839,0.109199523925781,-0.109161376953125,0.109199523925781,0.109161376953125,-0.025000000372529,0.0249999985098839,-0.025000000372529,-0.025000000372529,-0.109199523925781,0.109161376953125,-0.109199523925781,-0.109161376953125,-0.025000000372529,-0.025000000372529,0.0249999985098839,-0.025000000372529,-0.109199523925781,-0.109161376953125,0.109199523925781,-0.109161376953125,0.109199523925781,0.746934652328491,-0.109199523925781,0.746934652328491,0,0.926843762397766,0.109161376953125,0.746792256832123,-0.109161376953125,0.746792256832123,0,0.926724493503571,0.109161376953125,0.746792256832123,-0.109161376953125,0.746792256832123,0,0.926724493503571,0.109199523925781,0.746934652328491,-0.109199523925781,0.746934652328491,0,0.926843762397766
			} 
			UVIndex: *56 {
				a: 0,2,3,1,4,6,7,5,8,10,11,9,12,14,15,13,16,18,19,17,36,38,37,39,41,40,42,44,43,45,47,46,20,23,21,23,20,22,24,27,25,27,24,26,28,31,29,31,28,30,32,35,33,35,32,34
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *17 {
				a: 0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2311193850160, "Model::Bluemon_Spear_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",-48.4606018066406,44.0890808105469,109.413757324219
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1.00000143051147,1.00000083446503,1.00000047683716
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2310093295712, "Material::black_light", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0,0
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0,0
			P: "Opacity", "double", "Number", "",1
		}
	}
	Material: 2310093298112, "Material::Blue_Light", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0.0219626426696777,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0.0219626426696777,1
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Bluemon_Spear_Model, Model::RootNode
	C: "OO",2311193850160,0
	
	;Material::black_light, Model::Bluemon_Spear_Model
	C: "OO",2310093295712,2311193850160
	
	;Material::Blue_Light, Model::Bluemon_Spear_Model
	C: "OO",2310093298112,2311193850160
	
	;Geometry::Scene, Model::Bluemon_Spear_Model
	C: "OO",2311113378112,2311193850160
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
