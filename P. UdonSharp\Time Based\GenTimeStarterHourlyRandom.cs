using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class GenTimeStarterHourlyRandom : UdonSharpBehaviour
{
    public GameObject[] TimedEvents;

    [UdonSynced] public int currentActiveEvent = -1;
    [UdonSynced] public int syncedHour = -1;

    void Start()
    {
        if (Networking.IsMaster){SendCustomEventDelayedSeconds(nameof(UpdateTime), 30f);}
        SendCustomEventDelayedSeconds(nameof(ApplyEvents), 30f);
        if (!Networking.IsMaster){RequestSerialization();}
    }

    public void UpdateTime()
    {
        if (!Networking.IsMaster) return;

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        int currentHour = networkTime.Hour;

        if (currentHour != syncedHour)
        {
            syncedHour = currentHour;
            currentActiveEvent = Random.Range(0, TimedEvents.Length);
            ApplyEvents();
            RequestSerialization();
        }

        SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);
    }

    public void ApplyEvents()
    {
        for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null) {TimedEvents[i].SetActive(false); }}
        if (currentActiveEvent >= 0 && currentActiveEvent < TimedEvents.Length && TimedEvents[currentActiveEvent] != null) {TimedEvents[currentActiveEvent].SetActive(true);}
    }

    public void DeleteEverything(){
        for (int i = 0; i < TimedEvents.Length; i++){
            if (TimedEvents[i] != null){Destroy(TimedEvents[i]);}
        }
        Destroy(gameObject);
    }
}