using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class GenTimeStarterHourlyRandom : UdonSharpBehaviour
{
    public int lastHour = -1;
    public int LastEvent;
    public GameObject[] TimedEvents;
    
    [UdonSynced] private int CurrentEvent; // Synced across all clients


    void Start()
    {
        // Syncing Function
        if (Networking.IsMaster){CurrentEvent = Random.Range(0, TimedEvents.Length); RequestSerialization();}

        SendCustomEventDelayedSeconds(nameof(ApplyEvent), 30f); UpdateTime();
    }


    public void UpdateTime()
    {
        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();

        // Hour change check
        if (networkTime.Second == 30 && networkTime.Minute == 0 && networkTime.Hour != lastHour)
        {
            if (Networking.IsMaster && TimedEvents != null && TimedEvents.Length > 0)
            {
                int NewEvent = Random.Range(0, TimedEvents.Length);
                // Avoid same weather twice
                if (NewEvent == CurrentEvent){CurrentEvent = (CurrentEvent + 1) % TimedEvents.Length;}
                else{CurrentEvent = NewEvent;}
                RequestSerialization();
            }

            SendCustomEventDelayedSeconds(nameof(ApplyEvent), 30f);

            lastHour = networkTime.Hour;
        }

        SendCustomEventDelayedSeconds(nameof(UpdateTime), 1f);
    }

    public override void OnDeserialization(){SendCustomEventDelayedSeconds(nameof(ApplyEvent), 30f);}

    public void ApplyEvent()
    {
        if (CurrentEvent != LastEvent && TimedEvents != null && TimedEvents.Length > 0)
        {
            // Deactivate all weather objects except current
            for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null){TimedEvents[i].SetActive(i == CurrentEvent);}}

            LastEvent = CurrentEvent;
        }
    }

    public void DeleteEverything(){
        for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null){Destroy(TimedEvents[i]);}}
        Destroy(gameObject);
    }
}