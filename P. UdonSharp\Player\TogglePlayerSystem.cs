﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class TogglePlayerSystem : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public SecurityCheckSystem SecurityCheckSystem;
    public PlayerWorldStartSystem playerWorldStartSystem;

    //Toggles
    public Toggle FriendlyFireToggle;

    //GameObjects
    public GameObject DeathPrison, RandomizeSky, CanChangePvPObject, CanChangePvPObjectLock;

    //Sounds
    public AudioClip FriendlyFireVoice1, FriendlyFireVoice2, PrisonVoice1, PrisonVoice2;
    public AudioSource AudioSource;

    void Start(){localPlayer = Networking.LocalPlayer;}

    public void EnableFriendlyFireEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableFriendlyFire));}
    public void DisableFriendlyFireEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableFriendlyFire));}
    public void EnableDeathPrisonEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableDeathPrison));}
    public void DisableDeathPrisonEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableDeathPrison));}
    public void ClearPrisonEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ClearPrison));}
    public void EnableRandomizeSkyEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableRandomizeSky));}
    public void DisableRandomizeSkyEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableRandomizeSky));}
    public void HealedPlayerEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(HealedPlayer));}
    public void SetPlayerto1KOEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(SetPlayerto1KO));}
    public void ForceFlingEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ForceFling));}

    public void EnableFriendlyFire(){
        FriendlyFireToggle.isOn = false;
        playerWorldStartSystem.FriendlyFire = true;
        playerWorldStartSystem.FriendlyFireKnockbackToggle();
        CanChangePvPObject.SetActive(false);
        CanChangePvPObjectLock.SetActive(true);
        foreach (string targetName in SecurityCheckSystem.targetPlayerNames)
        {
            if (localPlayer.displayName == targetName)
            {
                CanChangePvPObject.SetActive(true);
                CanChangePvPObjectLock.SetActive(false);
                break;
            }
        }
        AudioSource.PlayOneShot(FriendlyFireVoice1);
    }

    public void DisableFriendlyFire(){
        FriendlyFireToggle.isOn = true;
        playerWorldStartSystem.FriendlyFire = false;
        playerWorldStartSystem.FriendlyFireKnockbackToggle();
        CanChangePvPObject.SetActive(true);
        CanChangePvPObjectLock.SetActive(false);
        AudioSource.PlayOneShot(FriendlyFireVoice2);
    }

    public void EnableDeathPrison(){DeathPrison.SetActive(true); AudioSource.PlayOneShot(PrisonVoice1);}
    public void DisableDeathPrison(){DeathPrison.SetActive(false); AudioSource.PlayOneShot(PrisonVoice2);}
    public void ClearPrison(){
        playerWorldStartSystem.PlayerInPrison = false;
        playerWorldStartSystem.PrisonCooldown = 0;
    }

    public void EnableRandomizeSky(){RandomizeSky.SetActive(true);}
    public void DisableRandomizeSky(){RandomizeSky.SetActive(false);}

    public void HealedPlayer(){
        playerWorldStartSystem.MAXHEALTH = 100;
        playerWorldStartSystem.HEALTH = playerWorldStartSystem.MAXHEALTH;
    }
    public void SetPlayerto1KO(){
        playerWorldStartSystem.MAXHEALTH = 1;
        playerWorldStartSystem.HEALTH = playerWorldStartSystem.MAXHEALTH;
    }

    public void ForceFling(){localPlayer.SetVelocity(Vector3.up * 15);}
}
