﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalEnemySystem : UdonSharpBehaviour
{
    public int Health,HealthMax = 100;
    public bool HasInterstellarArmor;
    public bool IsDead;
    public float DeathDelay = 1f;

    //Effects
    public ParticleSystem Particle;
    public ParticleSystem Particle2;
    public AudioSource AudioSource;
    public GameObject[] Body;
    public Collider collider;
    public Rigidbody rb;
    public GameObject[] DamageAreas;
    public LayerMask damageLayer;
    public GameObject EnemyMainObject;

    //HealthBar
    public bool HealthBarDeattached;
    public GameObject HealthBar;
    public TextMeshPro DamageText;
    public Slider HealthSlider;
    public bool CanBeHit, CanDie;
    public float ImmunityFramesDoneDelay = 0.1f;

    //Extras
    public int HealthMode; //0 = NormalHealth, 1 = RandomHealth
    public int RandomHealthMin = 100, RandomHealthMax = 200;
    public bool DistanceRestriction;
    public float DistanceRestrictionRange = 10f;

    //Damage
    public int Damage;

    public void Start()
    {
        rb = GetComponent<Rigidbody>();
        if(HealthMode == 0){Health = HealthMax;}
        if(HealthMode == 1){HealthMax = Random.Range(RandomHealthMin, RandomHealthMax);}
        Health = HealthMax;
    
        if(DamageText != null){DamageText.text = Health.ToString() + "/" + HealthMax.ToString();}
        if(HealthSlider != null){HealthSlider.maxValue = HealthMax;}
        if(HealthSlider != null){HealthSlider.value = Health;}
        CanBeHit = true;
    
        if(DistanceRestriction){
            HealthBar.SetActive(false); 
            CanDie = false;
            SendCustomEventDelayedSeconds(nameof(CustomUpdate), 1f);
        }
        else{
            HealthBar.SetActive(true); 
            CanDie = true;
        }

        if(HealthBarDeattached){HealthBar.transform.SetParent(null); SendCustomEventDelayedSeconds(nameof(UpdateHealthBar), 0.1f);}
    }
    public void UpdateHealthBar()
    {
        HealthBar.transform.position = transform.position + new Vector3(0, 1.5f, 0);
        SendCustomEventDelayedSeconds(nameof(UpdateHealthBar), 0.02f);
    }

    public void CustomUpdate()
    {
        if(DistanceRestriction && !IsDead){
            if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < DistanceRestrictionRange){
                HealthBar.SetActive(true); 
                CanDie = true;
            }
            else{
                HealthBar.SetActive(false); 
                CanDie = false;
                if(IsDead == false){
                    if(HealthSlider != null){HealthSlider.maxValue = HealthMax;}
                    if(HealthSlider != null){HealthSlider.value = Health;}
                    if(DamageText != null){DamageText.text = Health.ToString() + "/" + HealthMax.ToString();}
                    Health = HealthMax;
                }
            }
        }
        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 1f);
    }





    public void ImmunityFramesDone(){CanBeHit = true;}
    public void Dead()
    {
        IsDead = true;
        if(Particle != null){Particle.Play();}
        for (int i = 0; i < Body.Length; i++){Body[i].SetActive(false);}
        collider.enabled = false;
        if(rb != null){rb.isKinematic = true;}
        HealthBar.SetActive(false);
        if(DamageAreas != null){
            for (int i = 0; i < DamageAreas.Length; i++){DamageAreas[i].SetActive(false);}
        }
        SendCustomEventDelayedSeconds(nameof(Delete), DeathDelay);
    }
    public void Delete(){
        if(EnemyMainObject != null){Destroy(HealthBar); Destroy(EnemyMainObject); Destroy(HealthBar);}
        else{Destroy(HealthBar); Destroy(gameObject);}
    }

    public void OnDestroy()
    {
        Destroy(HealthBar);
        Destroy(gameObject);
    }

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && CanDie && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && !IsDead)
        {
            if(!HasInterstellarArmor){
                Damage = DamageInput.Damage;
            }
            else if(HasInterstellarArmor){
                if(!DamageInput.CanPenetrateIntestellarArmor)
                {
                    Damage = DamageInput.Damage/3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
                else
                {
                    Damage = DamageInput.Damage*3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
            }
            Health -= Damage;
            if(DamageText != null){DamageText.text = Health.ToString() + "/" + HealthMax.ToString();}
            if(HealthSlider != null){HealthSlider.value = Health;}
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(DamageInput == null && CanBeHit && CanDie && !IsDead)
        {
            Damage = 1;
            Health -= Damage;
            if(DamageText != null){DamageText.text = Health.ToString() + "/" + HealthMax.ToString();}
            if(HealthSlider != null){HealthSlider.value = Health;}
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && CanDie && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !IsDead)
        {
            if(!HasInterstellarArmor){
                Damage = DamageInput.Damage;
            }
            else if(HasInterstellarArmor){
                if(!DamageInput.CanPenetrateIntestellarArmor)
                {
                    Damage = DamageInput.Damage/3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
                else
                {
                    Damage = DamageInput.Damage*3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
            }
            Health -= Damage;
            if(DamageText != null){DamageText.text = Health.ToString() + "/" + HealthMax.ToString();}
            if(HealthSlider != null){HealthSlider.value = Health;}
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}
