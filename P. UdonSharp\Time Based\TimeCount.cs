using UdonSharp;
using UnityEngine;
using TMPro;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class TimeCount : UdonSharpBehaviour
{
    [SerializeField, Tooltip("Array of TextMeshPro components to display time")]
    public TextMeshPro[] timeTextTMP;
    public int lastHour = -1;
    public int LastWeather;
    public GameObject[] Weather;
    public PopupSystemOutput PopupSystemOutput;
    
    [UdonSynced] private int CurrentWeather; // Synced across all clients
    
    //Jeff Stuff
    public string[] Jeffquotes;

    //Car Traffic Events
    public TrafficRandomizer[] TraffRandomizers;

    void Start()
    {
        // Syncing Function
        if (Networking.IsMaster){CurrentWeather = 0; RequestSerialization();}

        ApplyWeather(); UpdateTime();
    }

    public void UpdateTime()
    {
        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        string formattedTime = networkTime.ToString("HH:mm:ss");

        // Update all time displays
        if (timeTextTMP != null){foreach (TextMeshPro text in timeTextTMP){if (text != null) text.text = formattedTime;}}

        // Hour change check
        if (networkTime.Second == 0 && networkTime.Minute == 0 && networkTime.Hour != lastHour)
        {
            if (Networking.IsMaster && Weather != null && Weather.Length > 0)
            {
                int newWeather = Random.Range(0, Weather.Length);
                // Avoid same weather twice
                if (newWeather == CurrentWeather){CurrentWeather = (CurrentWeather + 1) % Weather.Length;}
                else{CurrentWeather = newWeather;}
                RequestSerialization();
            }

            ApplyWeather();

            // Update traffic if randomizers exist
            if (TraffRandomizers != null){foreach (TrafficRandomizer randomizer in TraffRandomizers){if (randomizer != null) randomizer.RandomizeTraffic();}}

            lastHour = networkTime.Hour;
        }

        SendCustomEventDelayedSeconds(nameof(UpdateTime), 1f);
    }

    public override void OnDeserialization(){ApplyWeather();}

    private void ApplyWeather()
    {
        if (CurrentWeather != LastWeather && Weather != null && Weather.Length > 0)
        {
            // Deactivate all weather objects except current
            for (int i = 0; i < Weather.Length; i++){if (Weather[i] != null){Weather[i].SetActive(i == CurrentWeather);}}

            // Show Jeff quote if popup system exists
            if (PopupSystemOutput != null && Jeffquotes != null && CurrentWeather < Jeffquotes.Length){PopupSystemOutput.ShowPopUp(Jeffquotes[CurrentWeather]);}

            LastWeather = CurrentWeather;
        }
    }
}

