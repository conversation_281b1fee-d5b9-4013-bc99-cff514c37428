﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SimpleThrowerAway : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public int ThrowerType = 2;
    public int Force;
    public bool Upwards; 
    public bool direction;


    void Start(){localPlayer = Networking.LocalPlayer;}

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;
        
        if(ThrowerType == 1){
            if(Upwards && !direction){
                Vector3 triggerPosition = transform.position + new Vector3(0,-50,0);
                Vector3 directionToPlayer = triggerPosition - localPlayer.GetPosition();
                Vector3 oppositeDirection = -directionToPlayer.normalized;
                localPlayer.SetVelocity(oppositeDirection * Force);
            }
            else if(!Upwards && !direction){
                Vector3 directionToPlayer = transform.position - (localPlayer.GetPosition() + new Vector3(0,2f,0));
                localPlayer.SetVelocity(directionToPlayer.normalized * Force);
            }

            if(direction){
                Vector3 launchDirection = -transform.up;
                localPlayer.SetVelocity(launchDirection * Force);
            }
        }
    }

    public override void OnPlayerTriggerStay(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

       if(ThrowerType == 2){
            if(Upwards && !direction){
                Vector3 triggerPosition = transform.position + new Vector3(0,-50,0);
                Vector3 directionToPlayer = triggerPosition - localPlayer.GetPosition();
                Vector3 oppositeDirection = -directionToPlayer.normalized;
                localPlayer.SetVelocity(oppositeDirection * Force);
            }
            else if(!Upwards && !direction){
                Vector3 directionToPlayer = transform.position - (localPlayer.GetPosition() + new Vector3(0,2f,0));
                localPlayer.SetVelocity(directionToPlayer.normalized * Force);
            }

            if(direction){
                Vector3 launchDirection = -transform.up;
                localPlayer.SetVelocity(launchDirection * Force);
            }
        }
    }
}
