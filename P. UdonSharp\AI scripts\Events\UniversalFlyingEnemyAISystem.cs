﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalFlyingEnemyAISystem : UdonSharpBehaviour
{
    public float speed,Turn;
    public float sightRange;
    private Vector3 SpawnedPoint;

    public int AIType; //0 Is Staring, 1 is Attacking
    public bool IsGlitchBot;

    private VRCPlayerApi localPlayer;
    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
        if(IsGlitchBot){
            SendCustomEventDelayedSeconds(nameof(RandomizeCoreType), 5f);
        }
    }
    public void RandomizeCoreType(){
        AIType = Random.Range(0,2);
        SendCustomEventDelayedSeconds(nameof(RandomizeCoreType), 5f);
    }

    public void UpdateTimer(){
        if(localPlayer == null) return;
        if(AIType == 0){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRange && Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRange / 3){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
            else if (Vector3.Distance(localPlayer.GetPosition(), transform.position) <= sightRange / 3){
                Vector3 Direction = (localPlayer.GetPosition() + new Vector3(0,1,0)) - transform.position;
                transform.rotation = Quaternion.LookRotation(Direction);
                GetComponent<Rigidbody>().velocity = Vector3.zero;
                float Animation = Mathf.Cos(Time.time*2);
                transform.position = transform.position + new Vector3(0, Animation/40, 0);
            }
            else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRange){
                Vector3 direction = (SpawnedPoint - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
        }
        else if(AIType == 1){
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position + new Vector3(Random.Range(-3,3),Random.Range(-3,3),Random.Range(-3,3))).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;
        }
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.02f);
    }
}
