﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class LongTimedAnimV2 : UdonSharpBehaviour
{
    public System.DateTime CurrentTime;
    public System.DateTime StartTime = new System.DateTime(2025, 6, 22, 0, 0, 0);
    public System.DateTime EndTime = new System.DateTime(2025, 7, 5, 20, 1, 0);
    public GameObject AnimObject,EventObject;
    public bool isMovable;
    public Transform[] Positions;
    [Range(0f, 1f)]
    public float progress; // normalized float from 0 to 1
    public int EventIsPlaying;

    public int segmentCount;
    public float segmentLength;

    public TextMeshPro[] timeTextTMP;

    void Start()
    {
        EventObject.SetActive(false);
        StartTime = new System.DateTime(2025, 6, 19, 0, 0, 0, System.DateTimeKind.Utc);
        EndTime = new System.DateTime(2025, 7, 5, 20, 1, 0, System.DateTimeKind.Utc);
        if(AnimObject != null && isMovable){
            segmentCount = Positions.Length - 1;
            segmentLength = 1f / segmentCount;
        }
        UpdateTimer();
    }

    public void UpdateTimer()
    {
        System.DateTime CurrentTime = Networking.GetNetworkDateTime().ToUniversalTime();
        System.TimeSpan TimeSpan = EndTime.Subtract(CurrentTime);
        
        if (TimeSpan.TotalSeconds <= 0){
            for(int i = 0; i < timeTextTMP.Length; i++){timeTextTMP[i].gameObject.SetActive(false);}
            if(AnimObject != null){AnimObject.SetActive(false);}
            EventObject.SetActive(true);
            EventIsPlaying = 1;
        }
        else{for(int i = 0; i < timeTextTMP.Length; i++){timeTextTMP[i].text = TimeSpan.ToString("d':'h':'m':'s");}}

        if(AnimObject != null && isMovable){
            // Normalize the progress based on StartTime and EndTime
            System.TimeSpan totalDuration = EndTime - StartTime;
            System.TimeSpan elapsedDuration = CurrentTime - StartTime;
            progress = Mathf.Clamp01((float)(elapsedDuration.TotalSeconds / totalDuration.TotalSeconds));

            // Determine which segment we're in
            int currentIndex = Mathf.FloorToInt(progress / segmentLength);
            if (currentIndex >= segmentCount) currentIndex = segmentCount - 1;

            float segmentStart = currentIndex * segmentLength;
            float segmentProgress = (progress - segmentStart) / segmentLength;

            // Get transforms to interpolate between
            Transform from = Positions[currentIndex];
            Transform to = Positions[currentIndex + 1];

            // Linearly interpolate position and rotation
            AnimObject.transform.position = Vector3.Lerp(from.position, to.position, segmentProgress);
        }

        if(EventIsPlaying == 0){SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);}
    }
}
