using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.UI;
using TMPro;
using VRCLinking;
using VRC.SDK3.Components;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class GlobalSpawnMenuSystem : UdonSharpBehaviour
{
    public string[] roleId;
    public string[] RoleName;
    public string[] members;
    public VrcLinkingDownloader linkingDownloader;

    public GameObject Menu;
    public GameObject[] Panels;

    public bool PlayerInProximity, IsActivated;

    private VRCPlayerApi localPlayer;

    public GameObject[] Props,Misc,Weapons;
    public GameObject[] proplimit;
    public GameObject LimitReachedText;
    public Transform spawnpoint;
    public AudioSource AudioSource;
    public AudioClip SpawnSound,buzz,ClickSound,RefreshSound;

    void Start()
    {
        for (int i = 0; i < Panels.Length; i++){Panels[i].SetActive(false);}
        Panels[0].SetActive(true);

        Menu.SetActive(false);
        localPlayer = Networking.LocalPlayer;

        SendCustomEventDelayedSeconds(nameof(ActivateMenu), 32f);
    }
    public void ActivateMenu(){
        for (int i = 0; i < roleId.Length; i++){
            if (linkingDownloader.TryGetGuildMembersByRoleId(roleId[i], out members))
            {
                foreach (string memberName in members)
                {
                        
                    if (localPlayer.displayName == memberName)
                    {
                        IsActivated = true;
                        break;
                    }
                }
            }
        }
    }

    //SyncingStuff
    #region PropsSync
    public void SpawnPropObject1Everyone(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(PropObject1));
                break;
            }
        }
        LimitChecker();
    }
    #endregion PropsSync

    public void DeleteObjectsEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DeleteObjects));}
    public void DeleteLastObjectEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DeleteLastObject));}





    #region Props
    public void PropObject1(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[0]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;

                VRCObjectSync objectSync = prop.GetComponent<VRCObjectSync>();
                if (objectSync != null)
                {
                    objectSync.FlagDiscontinuity();
                }

                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    #endregion Props
    #region Misc

    #endregion Misc
    #region Weapons

    #endregion Weapons

    //SpawnMenuExtras
    void LimitChecker()
    {
        bool allFull = true;

        for (int i = 0; i < proplimit.Length; i++)
        {
            if (proplimit[i] == null){allFull = false; break;}
        }

        if (allFull){LimitReachedText.SetActive(true); Buzzsound();}
        else{LimitReachedText.SetActive(false);}
    }
    public void DeleteObjects(){
        for (int i = 0; i < proplimit.Length; i++){if (proplimit[i] != null){Destroy(proplimit[i]);}}
        LimitReachedText.SetActive(false);
        AudioSource.PlayOneShot(RefreshSound);
    }
    public void DeleteLastObject(){
        for (int i = proplimit.Length - 1; i >= 0; i--){
            if (proplimit[i] != null){
                Destroy(proplimit[i]);
                break;
            }
        }
        LimitReachedText.SetActive(false);
        AudioSource.PlayOneShot(RefreshSound);
    }

    //SmallFunctions
    public void Buzzsound(){AudioSource.PlayOneShot(buzz);}
    public void Keysound(){AudioSource.PlayOneShot(ClickSound);}

    #region CollisionFunctions
    //Collision Related
    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if(player != localPlayer) return;
        if(!IsActivated) return;
        Menu.SetActive(true);
        PlayerInProximity = true;
    }
    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if(player != localPlayer) return;
        if(!IsActivated) return;
        Menu.SetActive(false);
        PlayerInProximity = false;
    }
    #endregion CollisionFunctions
}
