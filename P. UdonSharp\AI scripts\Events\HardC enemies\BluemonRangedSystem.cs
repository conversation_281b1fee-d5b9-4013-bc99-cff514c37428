﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BluemonRangedSystem : UdonSharpBehaviour
{
    public GameObject Object;
    public Transform ObjectTranform;
    public int HasLaunched;
    public float SightRange1a;
    public float SightRange1b;
    public float SightRange2a;
    public float SightRange2b;
    public float SightRange3a;
    public float SightRange3b;
    public float SightRange4a;
    public float SightRange4b;


    private VRCPlayerApi localPlayer;



    void Start(){
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.1f);
    }

    public void UpdateTimer()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange1a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange1b){
            if(HasLaunched == 0){
                Object.transform.position = ObjectTranform.position;
                Object.transform.rotation = ObjectTranform.rotation;
                Object.transform.parent = null;
                Object.SetActive(true);
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange2a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange2b){
            if(HasLaunched == 0){
                Object.transform.position = ObjectTranform.position;
                Object.transform.rotation = ObjectTranform.rotation;
                Object.transform.parent = null;
                Object.SetActive(true);
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange3a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange3b){
            if(HasLaunched == 0){
                Object.transform.position = ObjectTranform.position;
                Object.transform.rotation = ObjectTranform.rotation;
                Object.transform.parent = null;
                Object.SetActive(true);
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange4a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange4b){
            if(HasLaunched == 0){
                Object.transform.position = ObjectTranform.position;
                Object.transform.rotation = ObjectTranform.rotation;
                Object.transform.parent = null;
                Object.SetActive(true);
                HasLaunched = 1;
            }
        }
        else{
            if(HasLaunched == 1){
                Object.transform.position = ObjectTranform.position;
                Object.transform.rotation = ObjectTranform.rotation;
                Object.transform.parent = gameObject.transform;
                Object.SetActive(false);
                HasLaunched = 0;
            }
        }
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.1f);
    }

    public void OnDestroy(){
        Destroy(Object);
    }
}
