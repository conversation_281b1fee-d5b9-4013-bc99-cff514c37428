%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Spooky 3-3
  m_Shader: {fileID: 4800000, guid: 2fadcf1c16cff41549a438b367f06baa, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _KEYWORD_ENABLE_LITE_COLORING
  - _KEYWORD_ENABLE_LITE_QUALITY
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _LiteDistortionTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LiteOverlayTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Alpha: 0
    - _Clip: 0
    - _LiteAudioLinkBand: 0
    - _LiteAudioLinkMax: 1
    - _LiteAudioLinkMin: 0
    - _LiteAudioLinkModule: 0
    - _LiteAudioLinkPower: 0
    - _LiteBlurModule: 0
    - _LiteBlurPower: 0
    - _LiteBlurRadius: 1
    - _LiteBlurStyle: 3
    - _LiteBlurTransparency: 1
    - _LiteBorderModule: 0
    - _LiteBorderPower: 0
    - _LiteBorderSoften: 0
    - _LiteBorderStyle: 1
    - _LiteColoringBrightness: 0.24
    - _LiteColoringColorGrading: 0.576
    - _LiteColoringColorGradingTone: 0
    - _LiteColoringDarkness: 0.583
    - _LiteColoringDrain: 0
    - _LiteColoringEmission: 10
    - _LiteColoringHSVStyle: 0
    - _LiteColoringHSVh: 0
    - _LiteColoringHSVs: 0
    - _LiteColoringHSVv: 0
    - _LiteColoringInvert: 0
    - _LiteColoringModule: 1
    - _LiteColoringPosterization: 7.8
    - _LiteColoringRGBOverlayTransparency: 0.183
    - _LiteDistortionModule: 0
    - _LiteDistortionPowerX: 0.5
    - _LiteDistortionPowerY: 0.5
    - _LiteDistortionSpeedX: 0.5
    - _LiteDistortionSpeedY: 0.5
    - _LiteDistortionStyle: 3
    - _LiteDistortionTextureScale: 1
    - _LiteDistortionWobble: 0
    - _LiteDistortionWobbleCoverage: 50
    - _LiteDistortionWobblePower: 0
    - _LiteDistortionWobbleSpeed: 0
    - _LiteFilterAstral: 0
    - _LiteFilterAstralTransparency: 0.6
    - _LiteFilterAstralZoom: 0.063
    - _LiteFilterColorCrush: 0
    - _LiteFilterColorCrushPower: 0
    - _LiteFilterDuotone: 0
    - _LiteFilterDuotoneThreshold: 0.5
    - _LiteFilterDuotoneTransparency: 0
    - _LiteFilterFilm: 0
    - _LiteFilterFilmAmount: 0.003
    - _LiteFilterGradient: 0
    - _LiteFilterGradientTransparency: 1
    - _LiteFilterGrain: 1
    - _LiteFilterGrainAmount: 0.456
    - _LiteFilterModule: 0
    - _LiteFilterNeon: 1
    - _LiteFilterNeonHue: 1
    - _LiteFilterNeonTransparency: 0
    - _LiteFilterNeonWidth: 0.5
    - _LiteFilterOutline: 1
    - _LiteFilterOutlineTolerance: 2.64
    - _LiteFilterOutlineWidth: 0.32
    - _LiteFilterRainbow: 0
    - _LiteFilterRainbowSaturation: 1
    - _LiteFilterRainbowSpeed: 0.5
    - _LiteFilterVHS: 0
    - _LiteFilterVHSAmount: 20
    - _LiteFilterVignette: 1
    - _LiteFilterVignettePower: 0.038
    - _LiteFogDensity: 9.5
    - _LiteFogDistribution: 0.7
    - _LiteFogModule: 0
    - _LiteFogSafespace: 0
    - _LiteFogSafespaceSize: 10
    - _LiteGlitchAmount: 0.988
    - _LiteGlitchChromatic: 0
    - _LiteGlitchModule: 0
    - _LiteGlitchScale: 15
    - _LiteGlitchUVs: 0.25
    - _LiteOverlayAnimated: 0
    - _LiteOverlayFrames: 0
    - _LiteOverlayFramesX: 0
    - _LiteOverlayFramesY: 0
    - _LiteOverlayModule: 0
    - _LiteOverlayOffsetX: 0
    - _LiteOverlayOffsetY: 0
    - _LiteOverlayScrub: 0
    - _LiteOverlaySizeX: 1
    - _LiteOverlaySizeY: 1
    - _LiteOverlaySpeed: 0
    - _LiteOverlayTransparency: 0
    - _LiteRenderingFalloffEnd: 50
    - _LiteRenderingFalloffStart: 10
    - _LiteRenderingOOB: 1
    - _LiteRenderingPower: 0.7
    - _LiteRenderingQuality: 1
    - _LiteUVManipulationModule: 0
    - _LiteUVManipulationMoveX: 0
    - _LiteUVManipulationMoveY: 0
    - _LiteUVManipulationPixelation: 0
    - _LiteUVManipulationPixelationPower: 2000
    - _LiteUVManipulationRotation: 0
    - _LiteUVManipulationRotationAngle: 0
    - _LiteUVManipulationShakePowerX: 0
    - _LiteUVManipulationShakePowerY: 0
    - _LiteUVManipulationShakeSpeedX: 0
    - _LiteUVManipulationShakeSpeedY: 0
    - _LiteUVManipulationShakeStyle: 0
    - _LiteUVManipulationSpherize: 0
    - _LiteUVManipulationSpherizePower: 0
    - _LiteUVManipulationTransformationFlipX: 0
    - _LiteUVManipulationTransformationFlipY: 0
    - _LiteUVManipulationTransformationSlantBL: 0
    - _LiteUVManipulationTransformationSlantBR: 0
    - _LiteUVManipulationTransformationSlantTL: 0
    - _LiteUVManipulationTransformationSlantTR: 0
    - _LiteUVManipulationTransformationStretchX: 0
    - _LiteUVManipulationTransformationStretchY: 0
    - _LiteZoomModule: 0
    - _LiteZoomPower: 0
    - _LiteZoomRangeEnd: 10
    - _LiteZoomRangeStart: 5
    - _LiteZoomRangeStyle: 0
    - _Transparency: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 0}
    - _LiteBlurColor: {r: 1, g: 1, b: 1, a: 1}
    - _LiteBorderColor: {r: 0, g: 0, b: 0, a: 1}
    - _LiteColoringRGBMultiply: {r: 1, g: 1, b: 1, a: 1}
    - _LiteColoringRGBOverlay: {r: 0, g: 0, b: 0, a: 0}
    - _LiteFilterAstralColor: {r: 1, g: 1, b: 1, a: 1}
    - _LiteFilterDuotoneColorOne: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFilterDuotoneColorTwo: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFilterGradientLHS: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFilterGradientRHS: {r: 0.3207547, g: 0.3207547, b: 0.3207547, a: 1}
    - _LiteFilterGrainColor: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFilterOutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFilterVignetteColor: {r: 0, g: 0, b: 0, a: 1}
    - _LiteFogColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
