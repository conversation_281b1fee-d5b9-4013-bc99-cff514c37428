﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.AI;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BluetronAI : UdonSharpBehaviour
{

    public NavMeshAgent agent;
    public float sightRange = 30f, attackRange = 2f;
    public LayerMask whatIsPlayer;

    public int BluemonType; // 0 = Normal, 1 = Armored
    public int State;
    public float speed = 6f;
    public bool IsDead;

    private VRCPlayerApi localPlayer;

    //Effects
    public ParticleSystem Particle;
    public ParticleSystem Particle2;
    public AudioSource AudioSource;
    public GameObject Body;
    public Collider collider;
    public GameObject[] DamageAreas;
    public Animator animator;
    public float animatorvalue;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        collider = GetComponent<Collider>();

        if(BluemonType == 0){
            agent.speed = Random.Range(5f, 10f);
            float RandomScale = Random.Range(0.7f, 1.3f);
            transform.localScale = new Vector3(RandomScale,RandomScale,RandomScale);
        }

        SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
    }

    public void UpdateDestination()
    {
        agent.enabled = true;

        if (!agent.isOnNavMesh){Destroy(gameObject);}

        bool playerInSightRange = Physics.CheckSphere(transform.position, sightRange, whatIsPlayer);
        bool playerInAttackRange = Physics.CheckSphere(transform.position, attackRange, whatIsPlayer);

        if (playerInSightRange) { State = 1; }
        else if (!playerInSightRange) { State = 0; }

        if(!playerInAttackRange && !playerInSightRange && State == 0 && animatorvalue > 0){animatorvalue -= 0.05f;}
        else if(!playerInAttackRange && playerInSightRange && State == 1 && animatorvalue < 0.50f){animatorvalue += 0.05f;}
        else if(!playerInAttackRange && playerInSightRange && State == 1 && animatorvalue > 0.55f){animatorvalue -= 0.05f;}
        else if(playerInAttackRange && playerInSightRange && State == 1 && animatorvalue < 1f){animatorvalue += 0.1f;}

        animator.SetFloat("EnemyAnimationValue", animatorvalue);

        // Adjust update timing dynamically
        if(State == 0){
            if(!IsDead)
            {SetRandomDestination();}
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
        }
        else if (State == 1){
            if(!IsDead)
            {TargetedDestination();}
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
        }
    }

    void SetRandomDestination()
    {
        if (!agent.isOnNavMesh) return;

        Vector3 targetPosition = Random.insideUnitSphere * 100f + transform.position;
        agent.SetDestination(targetPosition);
    }
    void TargetedDestination()
    {
        if (!agent.isOnNavMesh) return;

        agent.SetDestination(localPlayer.GetPosition());
    }
}