using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CanBeWoreAsHatOutput : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public LayerMask LayerRequired;

    void Start(){localPlayer = Networking.LocalPlayer;}

    public void OnTriggerEnter(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        CanBeWoreAsHatInput CanBeWoreAsHatInput = collision.gameObject.GetComponent<CanBeWoreAsHatInput>();
        if(CanBeWoreAsHatInput != null && collision.gameObject.Networking.IsMaster)
        {
            collision.gameObject.transform.parent = transform;
            Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
            Collider[] colliders = collision.gameObject.GetComponents<Collider>();
            Collider collider = colliders[0];
            collider.isTrigger = true;
            rb.isKinematic = true;
        }
    }
    public void OnTriggerExit(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        CanBeWoreAsHatInput CanBeWoreAsHatInput = collision.gameObject.GetComponent<CanBeWoreAsHatInput>();
        if(CanBeWoreAsHatInput != null && collision.gameObject.Networking.IsMaster)
        {
            collision.gameObject.transform.parent = null;
            Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
            Collider[] colliders = collision.gameObject.GetComponents<Collider>();
            Collider collider = colliders[0];
            collider.isTrigger = false;
            rb.isKinematic = false;
        }
    }
}
