using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class Pokeball2 : UdonSharpBehaviour
{
    public VRC_Pickup pickup;
    public Rigidbody rb;
    public bool CanTeleport;

    //Actual Scrit
    private VRCPlayerApi localPlayer;  

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        rb = GetComponent<Rigidbody>();
        pickup = GetComponent<VRC_Pickup>();

        CanTeleport = true;
    }

    public override void OnPickup()
    {

    }

    public override void OnDrop(){
        if(CanTeleport == true){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(CustomUpdate));}
    }

    public void CustomUpdate(){
        if(CanTeleport == false){return;}
        localPlayer.TeleportTo(transform.position, localPlayer.GetRotation());
        CanTeleport = false;
        SendCustomEventDelayedSeconds(nameof(Allowpickup), 1800f);
    }

    public void Allowpickup(){CanTeleport = true;}
}