
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicBonePositioner : UdonSharpBehaviour
{
    public int BoneID; //0 is Head, 1 is LeftHand, 2 is RightHand, 3 is LeftFoot, 4 is RightFoot
    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
    }

    public void UpdateCustom()
    {
        if (localPlayer == null) return;

        if (BoneID == 0)
        {
            Vector3 headPos = localPlayer.GetBonePosition(HumanBodyBones.Head);
            if (headPos != Vector3.zero) transform.position = headPos; transform.rotation = localPlayer.GetBoneRotation(HumanBodyBones.Head);
        }
        
        if (BoneID == 1)
        {
            Vector3 leftHandPos = localPlayer.GetBonePosition(HumanBodyBones.LeftHand);
            if (leftHandPos != Vector3.zero) transform.position = leftHandPos;
        }
        
        if (BoneID == 2)
        {
            Vector3 rightHandPos = localPlayer.GetBonePosition(HumanBodyBones.RightHand);
            if (rightHandPos != Vector3.zero) transform.position = rightHandPos;
        }

        if (BoneID == 3)
        {
            Vector3 leftFootPos = localPlayer.GetBonePosition(HumanBodyBones.LeftFoot);
            if (leftFootPos != Vector3.zero) transform.position = leftFootPos;
        }

        if (BoneID == 4)
        {
            Vector3 rightFootPos = localPlayer.GetBonePosition(HumanBodyBones.RightFoot);
            if (rightFootPos != Vector3.zero) transform.position = rightFootPos;
        }

        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.05f);
    }
}



